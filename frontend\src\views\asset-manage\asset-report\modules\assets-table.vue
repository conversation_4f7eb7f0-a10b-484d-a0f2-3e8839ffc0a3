<script setup lang="tsx">
import { NButton } from 'naive-ui';
import { h, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useAppStore } from '@/store/modules/app';
import { useTable } from '@/hooks/common/table';
import { getUserAssets } from '@/service/api/assets';
// import TableHeaderOperation from '@/components/advanced/table-header-operation.vue';

const appStore = useAppStore();
const router = useRouter();

// 保存当前搜索参数
const currentSearchParams = ref({});
// 当前排序参数
const sortParams = ref({
  sortField: 'total_assets',
  sortOrder: 'desc'
});

// 处理排序参数，转换为API格式
function getSortParams(sorter: any) {
  if (sorter) {
    return {
      sortField: sorter.columnKey,
      sortOrder: sorter.order === 'descend' ? 'desc' : 'asc'
    };
  }
  return {
    sortField: 'total_assets',
    sortOrder: 'desc'
  };
}

const {
  columns,
  // columnChecks,
  data,
  loading,
  getData,
  mobilePagination
} = useTable({
  apiFn: async (params: any) => {
    try {
      // 转换请求参数
      const apiParams = {
        page: params.current,
        pageSize: params.size,
        ...sortParams.value,
        ...currentSearchParams.value
      };

      console.log('请求API参数:', apiParams);

      // 调用API
      const response = await getUserAssets(apiParams);

      // 确保返回结构符合 FlatResponseSuccessData<Api.Common.PaginatingQueryRecord<any>> 类型
      if (response && response.data) {
        return {
          error: null,
          data: {
            records: response.data.users || [],
            total: response.data.total || 0,
            size: response.data.pageSize || 10,
            current: response.data.page || 1
          }
        };
      }

      // 返回空数据结构
      return {
        error: null,
        data: {
          records: [],
          total: 0,
          size: 10,
          current: 1
        }
      };
    } catch (error: any) {
      // 确保错误也符合预期类型
      return {
        error,
        data: {
          records: [],
          total: 0,
          size: 10,
          current: 1
        }
      };
    }
  },
  showTotal: true,
  apiParams: {
    current: 1,
    size: 10
  },
  columns: () => [
    {
      key: 'company',
      title: '公司',
      align: 'center',
      minWidth: 100,
      width: 120,
      sorter: true
    } as any,
    {
      key: 'username',
      title: '昵称',
      align: 'center',
      minWidth: 100,
      width: 120,
      sorter: true
    } as any,
    {
      key: 'total_assets',
      title: '资产总数',
      align: 'center',
      width: 100,
      sorter: true,
      defaultSortOrder: 'descend'
    } as any,
    {
      key: 'image_count',
      title: '图片数量',
      align: 'center',
      width: 100,
      sorter: true
    } as any,
    {
      key: 'video_count',
      title: '视频数量',
      align: 'center',
      width: 100,
      sorter: true
    } as any,
    {
      key: 'audio_count',
      title: '音频数量',
      align: 'center',
      width: 100,
      sorter: true
    } as any,
    {
      key: 'actions',
      title: '操作',
      align: 'center',
      width: 120,
      render: (row: any) => {
        return h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            onClick: () => handleViewAssets(row)
          },
          { default: () => '查看资产' }
        );
      }
    } as any
  ]
});

// 处理表格排序
function handleSorterChange(sorter: any) {
  // 更新排序参数
  sortParams.value = getSortParams(sorter);

  // 重新加载数据
  getData();
}

// 查看用户资产的处理函数
function handleViewAssets(row: any) {
  // 导航到用户资产页面，传递用户ID
  router.push({
    name: 'asset-manage_user-report',
    query: {
      userid: row.id
    }
  });
}

// 更新表格方法，接收外部搜索参数
function updateTable(params: any) {
  // 保存当前搜索参数
  currentSearchParams.value = params;

  // 重置到第一页并刷新数据
  getData();
}

// 初始化时加载数据
onMounted(() => {
  getData();
});

// 暴露方法给父组件
defineExpose({
  updateTable
});
</script>

<template>
  <NCard title="用户资产统计" :bordered="false" size="small" class="card-wrapper">
    <!--
 <template #header-extra>
      <TableHeaderOperation
        v-model:columns="columnChecks"
        :disabled-delete="false"
        :loading="loading"
        @refresh="getData"
      />
    </template>
-->

    <NDataTable
      :columns="columns"
      :data="data"
      size="small"
      :flex-height="!appStore.isMobile"
      :scroll-x="702"
      :loading="loading"
      remote
      :row-key="(row: any) => row.username + row.company"
      :pagination="mobilePagination"
      class="table-container"
      @update:sorter="handleSorterChange"
    >
      <template #empty>
        <NEmpty description="暂无用户资产数据" />
      </template>
    </NDataTable>
  </NCard>
</template>

<style scoped lang="scss">
.card-wrapper {
  margin-top: 16px;
}

.table-container {
  height: calc(100vh - 32em); /* 估计FilterPanel和StatisticsOverview的总高度约为250px，根据实际情况调整 */
  // max-height: calc(100vh - 250px);
  // overflow: auto;
}
</style>
