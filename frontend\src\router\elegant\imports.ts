/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { RouteComponent } from "vue-router";
import type { LastLevelRouteKey, RouteLayout } from "@elegant-router/types";

import BaseLayout from "@/layouts/base-layout/index.vue";
import BlankLayout from "@/layouts/blank-layout/index.vue";

export const layouts: Record<RouteLayout, RouteComponent | (() => Promise<RouteComponent>)> = {
  base: BaseLayout,
  blank: BlankLayout,
};

export const views: Record<LastLevelRouteKey, RouteComponent | (() => Promise<RouteComponent>)> = {
  403: () => import("@/views/_builtin/403/index.vue"),
  404: () => import("@/views/_builtin/404/index.vue"),
  500: () => import("@/views/_builtin/500/index.vue"),
  "iframe-page": () => import("@/views/_builtin/iframe-page/[url].vue"),
  login: () => import("@/views/_builtin/login/index.vue"),
  about: () => import("@/views/about/index.vue"),
  aichat_assistant: () => import("@/views/aichat/assistant/index.vue"),
  "asset-manage_asset-info": () => import("@/views/asset-manage/asset-info/index.vue"),
  "asset-manage_asset-report": () => import("@/views/asset-manage/asset-report/index.vue"),
  "asset-manage_user-assets": () => import("@/views/asset-manage/user-assets/index.vue"),
  "asset-manage_user-report": () => import("@/views/asset-manage/user-report/index.vue"),
  audio_clearvoice: () => import("@/views/audio/clearvoice/index.vue"),
  audio_cosy: () => import("@/views/audio/cosy/index.vue"),
  audio_inspiremusic: () => import("@/views/audio/inspiremusic/index.vue"),
  audio_reduction: () => import("@/views/audio/reduction/index.vue"),
  audio_separate: () => import("@/views/audio/separate/index.vue"),
  audio_synthesis: () => import("@/views/audio/synthesis/index.vue"),
  audio_texttospeech: () => import("@/views/audio/texttospeech/index.vue"),
  audio_timbres: () => import("@/views/audio/timbres/index.vue"),
  audio_volcano: () => import("@/views/audio/volcano/index.vue"),
  "function_hide-child_one": () => import("@/views/function/hide-child/one/index.vue"),
  "function_hide-child_three": () => import("@/views/function/hide-child/three/index.vue"),
  "function_hide-child_two": () => import("@/views/function/hide-child/two/index.vue"),
  "function_multi-tab": () => import("@/views/function/multi-tab/index.vue"),
  function_request: () => import("@/views/function/request/index.vue"),
  "function_super-page": () => import("@/views/function/super-page/index.vue"),
  function_tab: () => import("@/views/function/tab/index.vue"),
  "function_toggle-auth": () => import("@/views/function/toggle-auth/index.vue"),
  home: () => import("@/views/home/<USER>"),
  manage_creditlog: () => import("@/views/manage/creditlog/index.vue"),
  manage_menu: () => import("@/views/manage/menu/index.vue"),
  manage_models: () => import("@/views/manage/models/index.vue"),
  manage_role: () => import("@/views/manage/role/index.vue"),
  manage_settings: () => import("@/views/manage/settings/index.vue"),
  "manage_system-config": () => import("@/views/manage/system-config/index.vue"),
  "manage_system-config_modules_ai-models-setting": () => import("@/views/manage/system-config/modules/ai-models-setting/index.vue"),
  "manage_user-credit": () => import("@/views/manage/user-credit/index.vue"),
  "manage_user-detail": () => import("@/views/manage/user-detail/[id].vue"),
  manage_user: () => import("@/views/manage/user/index.vue"),
  manage_workteams: () => import("@/views/manage/workteams/index.vue"),
  management_channels: () => import("@/views/management/channels/index.vue"),
  management_credit: () => import("@/views/management/credit/index.vue"),
  management_game: () => import("@/views/management/game/index.vue"),
  management_toolset: () => import("@/views/management/toolset/index.vue"),
  monitor_process: () => import("@/views/monitor/process/index.vue"),
  "multi-menu_first_child": () => import("@/views/multi-menu/first_child/index.vue"),
  "multi-menu_second_child_home": () => import("@/views/multi-menu/second_child_home/index.vue"),
  navigation: () => import("@/views/navigation/index.vue"),
  portrait_aidraw: () => import("@/views/portrait/aidraw/index.vue"),
  portrait_cutout: () => import("@/views/portrait/cutout/index.vue"),
  portrait_enlarge: () => import("@/views/portrait/enlarge/index.vue"),
  portrait_facecopy: () => import("@/views/portrait/facecopy/index.vue"),
  portrait_midjourney: () => import("@/views/portrait/midjourney/index.vue"),
  portrait_mimicbrush: () => import("@/views/portrait/mimicbrush/index.vue"),
  portrait_mjworks: () => import("@/views/portrait/mjworks/index.vue"),
  portrait_sdwebui: () => import("@/views/portrait/sdwebui/index.vue"),
  portrait_subtitle: () => import("@/views/portrait/subtitle/index.vue"),
  "share-page": () => import("@/views/share-page/index.vue"),
  stablediffusion: () => import("@/views/stablediffusion/index.vue"),
  statis_page: () => import("@/views/statis/page/index.vue"),
  statis_taskcall: () => import("@/views/statis/taskcall/index.vue"),
  text_copywriting: () => import("@/views/text/copywriting/index.vue"),
  text_imgocr: () => import("@/views/text/imgocr/index.vue"),
  text_translate: () => import("@/views/text/translate/index.vue"),
  "user-center": () => import("@/views/user-center/index.vue"),
  video_framepack: () => import("@/views/video/framepack/index.vue"),
  wiki_ainews: () => import("@/views/wiki/ainews/index.vue"),
  wiki_document: () => import("@/views/wiki/document/index.vue"),
};
