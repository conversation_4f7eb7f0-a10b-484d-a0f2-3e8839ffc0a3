import logging

from sqlalchemy import Column, Integer, String, Enum
from utils.database import Base

from typing import Union
from pydantic import BaseModel, ConfigDict
from fastapi import Depends, <PERSON><PERSON>, Request
import uuid

from utils.exceptions import ClientVisibleException
from utils.redis import redis
from utils.database import get_db
from sqlalchemy.orm import Session
from Crypto.Hash import RIPEMD160
import hmac
from config import endpoint_config

logger = logging.getLogger(__name__)


SECRET_KEY = "09ur293209ur03qefj2gj0t23tejgw904gnih"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_DAYS = 15

# oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")


class TokenData(BaseModel):
    username: Union[str, None] = None
    id: Union[int, None] = None


class UserOut(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    id: int
    username: str
    nickname: str
    avatar: str | None
    email: str | None
    group_id: int


class User(Base):
    __tablename__ = "user"
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, index=True)
    nickname = Column(String)
    password = Column(String)
    status = Column(Enum("normal", "locked", name="user_statuses"), default="normal")
    salt = Column(String)
    prevtime = Column(Integer)
    loginip = Column(String)
    logintime = Column(Integer)
    loginfailure = Column(Integer)
    avatar = Column(String)
    email = Column(String)
    mobile = Column(String)
    group_id = Column(Integer, default=1)
    jointime = Column(Integer)
    joinip = Column(String)
    verification = Column(String)


# 返回前端的用户信息


def get_token_key(token: str) -> str:
    h = hmac.new(endpoint_config.get("token").get("key").encode("utf-8"), digestmod=RIPEMD160)
    h.update(token.encode("utf-8"))
    encode_token = h.hexdigest()
    return "tp:%s" % encode_token


async def create_access_token(user: User):
    """
    生成用户token
    """
    # 保留最新的5个token , 取出第4个后面的token来删掉
    tokens = await redis.exec("lrange", "user:%s" % user.username, 4, -1)
    for token in tokens:
        await redis.exec("delete", token)
    # 调整列表的长度
    await redis.exec("ltrim", "user:%s" % user.username, 0, 3)
    # 生成新的token
    token = str(uuid.uuid4())
    token_key = get_token_key(token)
    await redis.set(token_key, user.id, ACCESS_TOKEN_EXPIRE_DAYS * 24 * 60 * 60)
    # 将最新的token放在队列的前面
    await redis.exec("lpush", "user:%s" % user.username, token_key)
    return token


async def clear_user_tokens(username: str):
    """
    清除用户的token
    """
    tokens = await redis.exec("lrange", "user:%s" % username, 0, -1)
    for token in tokens:
        await redis.exec("delete", get_token_key(token))


async def get_request_user(
    x_token: str = Header(), request: Request = None, db: Session = Depends(get_db)
):
    credentials_exception = ClientVisibleException("Could not validate credentials")
    # payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
    # get userid from redis
    userid = await redis.get(get_token_key(x_token))
    if userid is None:
        logger.error("token(%s) expired" % x_token)
        raise credentials_exception
    # get user from database
    user = db.query(User).filter(User.id == userid).first()
    if user is None:
        logger.error("userid(%s) is not exist" % userid)
        raise credentials_exception

    if user.status != "normal":
        logger.error("user(%s) is locked" % user.username)
        raise credentials_exception

    request.state.token = x_token
    return user


async def destory_token(token: str):
    await redis.delete(get_token_key(token))


async def extension_token_ttl(token: str):
    await redis.exec(
        "expire", get_token_key(token), ACCESS_TOKEN_EXPIRE_DAYS * 24 * 60 * 60
    )
