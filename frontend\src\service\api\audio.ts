import { request } from '../request';

export interface VoiceExampleItem {
  name: string;
  file_name: string;
  content: string;
  file_path: string;
}
export interface RoleVoiceExampleItem {
  [key: string]: VoiceExampleItem[];
}
export interface VoiceExampleResult {
  role: string[];
  voice_example: RoleVoiceExampleItem;
}
export interface GPTSoVITSTTSREsult {
  b64: string;
}
export interface CHATTTSREsult {
  url: string;
}
// ** 提取声音样例 */
export function getRoleVoiceExample() {
  return request<VoiceExampleResult>({
    url: '/media/get_role_voice_example',
    method: 'get'
  });
}

export function postTTS(data: any) {
  return request<GPTSoVITSTTSREsult>({
    url: '/media/tts',
    method: 'post',
    data
  });
}
export function postChatTTS(data: any) {
  return request<CHATTTSREsult>({
    url: '/media/chat_tts',
    method: 'post',
    data
  });
}

// ***************************************** COSY *****************************************

export interface SpeakersResult {
  default_speakers: Record<string, { audio: string; model: string }>;
  custom_speakers: Record<string, { audio: string; model: string }>;
}
export interface CosyResult {
  b64: string;
  type: string;
}
export interface SoVITSZeroShotResult {
  b64: string;
}
export interface SeparateResult {
  instrumental: string;
  vocals: string;
  type: string;
}

export function getSpeaker() {
  return request<SpeakersResult>({
    url: '/cosy/speaker',
    method: 'get'
  });
}
export function postSeparate(data: Api.Audio.Separate) {
  return request<SeparateResult>({
    url: '/media/voice_separate',
    method: 'post',
    data
  });
}
export function postCosyTast(data: Api.Audio.Cosy) {
  return request<CosyResult>({
    url: '/cosy/task',
    method: 'post',
    data
  });
}
export function postZeroShotTast(data: Api.Audio.Cosy) {
  return request<CosyResult>({
    url: '/cosy/zero_shot',
    method: 'post',
    data
  });
}

// SoVITS ZeroShot
export function postSoVITSZeroShotTast(data: Api.Audio.SoVITSZeroShot) {
  return request<SoVITSZeroShotResult>({
    url: '/media/sovits_zero_shot',
    method: 'post',
    data
  });
}

export function getUserModels() {
  return request({
    url: '/media/user_model',
    method: 'get'
  });
}

// ***************************************** 火山 *****************************************

export function postVolcanoTTS(data: Api.Audio.VolcanoTTS) {
  return request<Api.Audio.VolcanoTTSResult>({
    url: '/volcano/tts',
    method: 'post',
    data
  });
}

// ***************************************** 音频降噪 *****************************************
// 提交音频降噪任务
export function postReduction(data: Api.Audio.Reduction) {
  return request({
    url: '/reduction/noise_reduction',
    method: 'post',
    data
  });
}

// 查询音频降噪任务状态
export function getReductionStatus(job_id: string) {
  return request({
    url: `/reduction/reduction_status/${job_id}`,
    method: 'get'
  });
}

// 获取音频降噪任务历史
export function getHistory(page: number, size: number) {
  return request({
    url: '/reduction/history',
    method: 'get',
    params: {
      page,
      size
    }
  });
}

// ***************************************** clearvoice *****************************************
export function postEnhancement(data: Api.Audio.Separate) {
  return request({
    url: '/clearvoice/enhancement',
    method: 'post',
    data
  });
}

export function postSeparation(data: Api.Audio.Separate) {
  return request({
    url: '/clearvoice/separation',
    method: 'post',
    data
  });
}

// ***************************************** inspiremusic *****************************************
// Define a more specific type for the task data if not available globally
interface MusicTask {
  id: number;
  taskid: string;
  status: 'NOT_START' | 'SUBMITTED' | 'IN_PROGRESS' | 'FAILURE' | 'SUCCESS';
  prompt?: string | null;
  music_title: string;
  audio_data?: { url: string; type: string; duration?: number; sample_rate?: number } | null; // Assuming audio_url is parsed into audio_data
  fail_reason?: string | null;
  queue_position?: number | null;
  submit_time?: string | null;
}

// interface MusicTaskResponse {
//   code: string;
//   data?: MusicTask | null;
//   msg?: string;
// }

// interface MusicHistoryResponse {
//   code: string;
//   data?: MusicTask[] | null; // Expecting an array of tasks
//   msg?: string;
// }

interface InspireMusicGenerateData {
  text: string;
  music_title?: string;
  model_name?: string;
  chorus?: string;
  output_sample_rate?: number;
  max_generate_audio_seconds?: number;
}

interface InspireMusicContinueData extends InspireMusicGenerateData {
  audio_base64: string;
}

export function postMusicGenerate(data: InspireMusicGenerateData) {
  return request<MusicTask | null>({
    url: '/inspiremusic/generate',
    method: 'post',
    data
  });
}

export function postMusicContinue(data: InspireMusicContinueData) {
  return request<MusicTask | null>({
    url: '/inspiremusic/continue',
    method: 'post',
    data
  });
}

export function getMusicPrompts() {
  return request({
    url: '/inspiremusic/prompts',
    method: 'get'
  });
}

export function getMusicTaskStatus(taskid: string) {
  return request<MusicTask | null>({
    url: `/inspiremusic/task_status/${taskid}`,
    method: 'get'
  });
}

// 获取历史记录
export function getMusicHistory(page: number, size: number, title?: string) {
  return request<MusicTask[] | null>({
    url: '/inspiremusic/history',
    method: 'get',
    params: { page, size, title }
  });
}

// *** 音色库 ***

// 语言枚举
export enum Lang {
  CMN = '中文-普通话',
  YUE = '中文-粤语',
  ENG = '英语',
  JPN = '日语',
  KOR = '韩语'
}

// 音色信息
export interface SaveTone {
  id: number;
  name: string;
  gender: 0 | 1; // 0: 女性, 1: 男性
  lang: Lang;
  description: string;
  create_time: string;
  update_time: string;
  is_favorite: boolean;
  audio_url: string;
  is_reduction: boolean;
}

// 创建/修改音色请求
export interface ModifyToneReq {
  name: string;
  gender: 0 | 1;
  lang: Lang | null;
  description: string;
  audio_url: string;
  is_favorite: boolean;
  need_reduction?: boolean;
}

// 音色列表查询参数
export interface TonesListParams {
  size?: number;
  current?: number;
  gender?: 0 | 1;
  lang?: Lang;
  is_favorite?: number;
}

// 获取音色列表
export function getTonesList(params?: TonesListParams) {
  return request<Api.Common.PaginatingQueryRecord<SaveTone>>({
    url: '/tones/',
    method: 'get',
    params
  });
}

// 获取单个音色
export function getTone(toneId: number) {
  return request<SaveTone>({
    url: `/tones/${toneId}`,
    method: 'get'
  });
}

// 创建音色
export function createTone(data: ModifyToneReq) {
  return request<SaveTone>({
    url: '/tones/',
    method: 'post',
    data
  });
}

// 更新音色
export function updateTone(toneId: number, data: ModifyToneReq) {
  return request<SaveTone>({
    url: `/tones/${toneId}`,
    method: 'put',
    data
  });
}

// 删除音色
export function deleteTone(toneId: number) {
  return request<SaveTone>({
    url: `/tones/${toneId}`,
    method: 'delete'
  });
}
