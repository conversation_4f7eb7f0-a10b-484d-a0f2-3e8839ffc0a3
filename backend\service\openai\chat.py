import asyncio
import json
import logging
import re
from enum import StrEnum
import html
from functools import partial

import nanoid

from config import app_settings
from models.assets import AssetType
from utils.asset_storage import sync_store_asset_directly
from utils.hash.aes import decrypt
from models.chat import chatContent
from typing import List
from openai import OpenAI
from openai.types.chat import ChatCompletionStreamOptionsParam
from .prompts import get_system_prompt, WEB_SEARCH_PROMPT, GEN_IMG_PROMPT
import tiktoken
from .mcp import list_tools, call_tool
# from service.baidu.search_mcp import WEB_SEARCH_TOOL, do_web_search, web_search_result_formatter
from service.zhipu.web_search import WEB_SEARCH_TOOL, do_web_search, web_search_result_formatter
from service.comfyui.chat_tool import GEN_IMG_TOOL, do_gen_img

logger = logging.getLogger(__name__)


def get_token():
    return decrypt(app_settings.openai_api_key)

def get_base_url():
    return app_settings.openai_api_base_url


def num_tokens_from_string(string: str, encoding_name: str = "cl100k_base") -> int:
    """返回给定字符串的token数量"""
    encoding = tiktoken.get_encoding(encoding_name)
    num_tokens = len(encoding.encode(string))
    return num_tokens

think_models = ["o1-mini", "deepseek-r1", "o3-mini"]

# base_url = "https://osa.originmood.com/openai/v1"

client = OpenAI(api_key=get_token(), base_url=get_base_url())

# o3_prompt = """
# In every output, response using the following format:
# <think>
# {reasoning_content}
# </think>

# {content}
# """


def chat(
        model_name: str,
        contents: List[chatContent],
        systemPrompt: str = "",
        memory: str = "name is 胖大星",
        temperature: float = 0.8,
        mcp: bool = False,
        web_search: bool = False,
        loop: asyncio.AbstractEventLoop = None,
        assets_storage_func: partial[type[sync_store_asset_directly]] | None = None,
):
    """
    集成了deepseek-r1模型思维链处理的聊天函数

    Args:
        model_name: 模型名称
        contents: 对话历史
        systemPrompt: 系统提示语
        memory: 记忆内容(DeepSeek-R1建议不使用system prompt)
        temperature: 温度参数，控制回复的随机性
        mcp: 是否使用 MCP
        web_search: 是否使用联网搜索
        loop: 事件循环
        assets_storage_func: 存储资产的方法，参数参考 utils.asset_storage.sync_store_asset_directly
    """
    # DeepSeek-R1建议不添加system prompt
    if "deepseek-r1" in model_name:
        messages = []
    else:
        system_prompt = get_system_prompt(model_name, memory)
        if systemPrompt:
            system_prompt = systemPrompt
        messages = [{"role": "system", "content": system_prompt}]

    # 添加对话历史
    for r in contents:
        if "deepseek-r1" in model_name:
            # DeepSeek-R1使用普通文本格式
            content = {
                "role": "user" if r.role == "user" else "assistant",
                "content": r.content,
            }
        else:
            # 其他模型使用支持多模态的格式
            content = {
                "role": "user" if r.role == "user" else "assistant",
                "content": [{"type": "text", "text": r.content}],
            }
            images = []
            if r.files:
                for f in r.files:
                    if f.type == "image":
                        content["content"].append({"type": "image_url", "image_url": {"url": f.url}})
                        images.append(f.url)
            if images:
                # 把图片链接添加到提示词里，便于进行 Tool Call
                content['content'][0]['text'] += f"\n\n 上传的图片对应的 URL：\n {'\n'.join(images)} \n\n"
        messages.append(content)

    tool_args = dict()
    # Gemini 除 2.5 Pro 之外暂时不支持，DeepSeek 支持有问题
    if "deepseek" not in model_name and ("gemini" not in model_name or model_name == "gemini-2.5-pro"):
        if mcp:
            tools_fut = asyncio.run_coroutine_threadsafe(list_tools(), loop)
            tools = tools_fut.result() or []
            for t in tools:
                tool_args.setdefault("tools", []).append({
                    "type": "function",
                    "function": {
                        "name": t.name,
                        "description": t.description or "",
                        "parameters": t.inputSchema,
                        "strict": True
                    }
                })
        if web_search:
            tool_args.setdefault("tools", []).append(WEB_SEARCH_TOOL)
            # 添加搜索引擎相关的提示词
            messages.append({
                "role": "system",
                "content": WEB_SEARCH_PROMPT,
            })
        # 添加图像生成功能
        tool_args.setdefault("tools", []).append(GEN_IMG_TOOL)
        messages.append({
            "role": "system",
            "content": GEN_IMG_PROMPT,
        })
        if tool_args.get("tools", None):
            tool_args["tool_choice"] = "auto"

    token_info = {
        "input_tokens": 0,
        "output_tokens": 0,
    }

    call_round = 0
    stop = False
    web_formatter = web_search_result_formatter()
    all_text = ''
    while call_round < app_settings.mcp_max_round and not stop:
        call_round += 1
        is_in_thinking = False  # 添加标志位，标记是否在思维链过程中
        # 创建对话请求
        completion = client.chat.completions.create(
            model=model_name,
            messages=messages,
            # max_tokens=4096,
            stream=True,
            stream_options=ChatCompletionStreamOptionsParam(include_usage=True),
            temperature=temperature,
            **tool_args,
        )

        # { tool_call_index: ["call id", "function name", "function argument"]}
        tool_calls_args: dict[int, list[str]] = dict()

        for chunk in completion:
            logger.debug(chunk)
            try:
                # 收集工具调用参数
                tool_calls = chunk.choices[0].delta.tool_calls
                if tool_calls is not None:
                    for idx, tc in enumerate(tool_calls):
                        arg = tool_calls_args.setdefault(idx, ["", "", ""])
                        if tc.id:
                            arg[0] += tc.id
                        if tc.function.name:
                            arg[1] += tc.function.name
                        if tc.function.arguments:
                            arg[2] += tc.function.arguments
                    continue

                # 进行工具调用
                if chunk.choices[0].finish_reason in {"tool_calls", "stop"} and tool_calls_args:
                    while tool_calls_args:
                        _, tool_call = tool_calls_args.popitem()
                        call_id, fn, args = tool_call
                        messages.append({
                            "role": "assistant",
                            "tool_calls": [{
                                "id": call_id,
                                "type": "function",
                                "function": {
                                    "name": fn,
                                    "arguments": args
                                }
                            }]
                        })
                        try:
                            if fn == WEB_SEARCH_TOOL['function']['name']:
                                yield format_block(BlockType.WEB_SEARCH)
                                search_fut = asyncio.run_coroutine_threadsafe(do_web_search(args), loop)
                                search_res = search_fut.result()
                                search_content = web_formatter(search_res)
                                yield format_block(
                                    BlockType.WEB_SEARCH_RESULT,
                                    content=search_content
                                )
                                if all_text:
                                    yield '\n'
                                messages.append({
                                    "role": "tool",
                                    "tool_call_id": call_id,
                                    "content": search_content,
                                })
                            elif fn == GEN_IMG_TOOL['function']['name']:
                                try:
                                    ds_args: dict = json.loads(args)
                                    assert isinstance(ds_args, dict), '生图参数不是 dict'
                                    if not all_text:
                                        yield f'好的，正在为您生成图片。'
                                    yield ds_args.get("prompt", "")
                                except (json.decoder.JSONDecodeError, AssertionError) as e:
                                    logger.error(f'无法解析生图参数：{args}， Error: {e}')
                                    raise e
                                yield format_block(BlockType.IMG_GENERATING, content=ds_args.get('aspect_ratio', '1:1'))
                                img_fut = asyncio.run_coroutine_threadsafe(do_gen_img(args), loop)
                                img_res = img_fut.result()
                                if len(img_res) == 0:
                                    raise ValueError('生成图片数量为 0')
                                if assets_storage_func is not None:
                                    assets_storage_func(
                                        asset_type=AssetType.IMAGE,
                                        url=img_res,
                                        taskid=nanoid.generate(),
                                        parameter={
                                            "prompt": ds_args.get("prompt", ""),
                                            "prompt_img": ds_args.get("image_url", ""),
                                            "aspect_ratio": ds_args.get("aspect_ratio", ""),
                                        },
                                    )
                                yield format_block(
                                    BlockType.IMG_GENERATION,
                                    content=json.dumps(img_res, ensure_ascii=False)
                                )
                                # 把生成的图片直接返回，会话结束
                                stop = True
                                break
                            else:
                                loaded_args = json.loads(args)
                                tool_call_fut = asyncio.run_coroutine_threadsafe(call_tool(fn, loaded_args), loop)
                                tool_call_res = tool_call_fut.result()
                                messages.append({
                                    "role": "tool",
                                    "tool_call_id": call_id,
                                    "content": json.dumps([c.model_dump() for c in tool_call_res], ensure_ascii=False),
                                })
                        except Exception as e:
                            logger.error(f"Error processing tool call: {e}")
                            yield "\n[工具调用出现错误]"
                            stop = True
                            break
                    break

                # 采集腾讯的搜索结果
                if result := chunk.choices[0].delta.model_extra.get('search_results'):
                    yield tencent_web_search_result_formatter(result)
                    if all_text:
                        yield '\n'

                # 处理思维链内容 (仅适用于deepseek-r1模型)
                if "deepseek-r1" in model_name and hasattr(chunk.choices[0].delta, "reasoning_content"):
                    text = getattr(chunk.choices[0].delta, 'reasoning_content')
                    if text:
                        # 如果不在思维链中，先输出开始标签
                        if not is_in_thinking:
                            yield "<think>\n"
                            is_in_thinking = True
                        # 直接输出思维链内容
                        t = replace_ref(text)
                        all_text += t
                        yield t

                # 处理普通回复内容
                elif hasattr(chunk.choices[0].delta, "content"):
                    # 如果之前在思维链中，先输出结束标签
                    if is_in_thinking:
                        yield "</think>"
                        is_in_thinking = False

                    text = chunk.choices[0].delta.content
                    if text is not None:
                        t = replace_ref(text)
                        all_text += t
                        yield t

                if chunk.choices[0].finish_reason == 'stop':
                    stop = True
                    # 统计 Token 使用
                    if chunk.usage:
                        # 有些平台 stop 这条 chunk 就会携带 usage 信息，如腾讯
                        usage = chunk.usage
                    else:
                        # 有些平台 stop 的下一条才是 usage 信息，如 OpenAI、Claude
                        try:
                            usage_chunk = next(completion)
                            usage = usage_chunk.usage
                        except StopIteration:
                            usage = None
                    if usage:
                        # 因为可能有多轮交互，所以这里是累加
                        token_info['input_tokens'] += usage.prompt_tokens
                        token_info['output_tokens'] += usage.completion_tokens
                    else:
                        logger.error('cannot get usage info')
                    break
            except Exception as e:
                logger.error(f"Error processing chunk: {e}")
                continue

        # 确保思维链结束时关闭标签
        if is_in_thinking:
            yield "</think>"

    if call_round >= app_settings.mcp_max_round:
        logger.error("工具使用轮次超出限制。")
        yield "\nERROR：工具使用轮次超出限制。"

    # 返回总token数
    yield token_info


ref_pattern = re.compile(r'\[\^(\d+)]')

def replace_ref(text: str) -> str:
    return ref_pattern.sub(format_block(BlockType.WEB_SEARCH_REF, content='{refId}').format(refId=r'\g<1>'), text)


class BlockType(StrEnum):
    WEB_SEARCH = 'web-search'
    WEB_SEARCH_REF = 'web-search-ref'
    WEB_SEARCH_RESULT = 'web-search-result'

    IMG_GENERATION = 'img-generation'
    """图片生成结果"""
    IMG_GENERATING= 'img-generating'
    """图片生成中"""


def format_block(block_type: BlockType, content: str = "", **kwargs) -> str:
    safe_content = html.escape(content)
    attrs = {k: html.escape(v) for k, v in kwargs.items()}
    attrs_str = " ".join([f'data-{k}="{v}"' for k, v in attrs.items()])
    if attrs_str:
        attrs_str = f" {attrs_str}"  # 前面加空格
    return f"""<aichat-{block_type}{attrs_str}>{safe_content}</aichat-{block_type}>"""


def tencent_web_search_result_formatter(results: list[dict[str, str | int]]) -> str:
    items = []
    for idx, result in enumerate(results, 1):
        items.append({
            "title": result.get('name', ''),
            "link": result.get('url', ''),
            "desc": result.get('snippet', ''),
            "icon": result.get('icon', ''),
            "media": result.get('site', ''),
            "refId": idx
        })
    return format_block(BlockType.WEB_SEARCH_RESULT, content=json.dumps(items, ensure_ascii=False))
