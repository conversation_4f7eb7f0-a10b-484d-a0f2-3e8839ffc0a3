{"version": "0.2.0", "configurations": [{"name": "Launch Chrome", "request": "launch", "type": "chrome", "url": "http://localhost:9527", "webRoot": "${workspaceFolder}/frontend"}, {"name": "Python Debugger: FastAPI", "type": "debugpy", "request": "launch", "module": "u<PERSON><PERSON>", "args": ["main:app", "--port", "5002", "--reload", "--host", "0.0.0.0"], "jinja": true, "cwd": "${workspaceFolder}/backend"}]}