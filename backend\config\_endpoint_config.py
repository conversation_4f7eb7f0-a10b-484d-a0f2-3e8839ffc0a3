endpoint_config = {
    "token": {
        "key": "HupomN81MkxEaj4vZitrOqyldFb0f3eJ",
        "hashalgo": "ripemd160"
    },
    "aiadmin_app_queue": {
        "queue_name": "aiadmin_app_tasks",
        "group_name": "aiadmin_tasks_group",
        "consumer_name": "aiadmin_app_tasks_consumer"
    },
    "api_map": {
      # 声音部分
      "/media/tts": "声音克隆-GPT_SoVITS",
      "/media/sovits_zero_shot": "声音复刻-GPT_SoVITS Zero Shot",
      "/media/chat_tts": "声音克隆-ChatTTS",
      "/media/voice_separate": "声音分离",
      "/cosy/zero_shot": "音频复制-3s极速复刻",
      "/cosy/task": "音频复制-预训练音色",
      "/volcano/tts": "火山语音",
      "/reduction/noise_reduction": "音频降噪",
      "/inspiremusic/generate": "音乐生成",
      "/inspiremusic/continue": "音乐续写",
      "/clearvoice/enhancement": "语音增强",
      "/clearvoice/separation": "语音分离",      

      # 文字
      "/text/translate": "AI翻译",
      "/text/generate_copywriting": "素材文案",
      "/media/extract_subtitle": "字幕提取",
      "/imgocr/extract_text": "文字提取",
      "/media/translate_subtitle": "字幕提取翻译",      

      # 图片
      "/cutout/get_cutout_img": "智能抠图",
      "/cutout/volcengine_cutout": "智能抠图",
      "/enlarge/volcengine_enlarge": "无损放大",
      "/enlarge/get_enlarge_file": "无损放大",
      "/facecopy/send_prompt": "表情迁移",
      "/mimicbrush/task": "模仿画笔",
      "/midjourney/submit/imagine": "Midjourney",
      "/midjourney/submit/change": "Midjourney",
      "/midjourney/submit/action": "Midjourney",
      "/midjourney/get_mj_prompt": "Midjourney",
      "/facecopy/isvedio":"表情迁移",
      "/gptimage/generate": "GPTImage1",
      "/gptimage/change/pan": "GPTImage1",
      "/flux/create": "Flux",
      "/flux/change/pan": "Flux",
      "/flux/comfyui": "Flux",

      # 聊天
      "/assistant/process": "助手",
      "/assistant/meta_prompt": "助手",
      "/volcano/ttstest":"扣费测试",
      "/volcano/ttype2":"类型2测试",
      "/volcano/ttype3":"返回500测试",

      # 视频
      "/framepack/generate": "FramePack",
      "/volcengine/generate": "即梦AI",
    }
}
