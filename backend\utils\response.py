from pydantic import BaseModel, Field


class SuccessRes[T: BaseModel](BaseModel):
    code: str = Field(default='0000')
    msg: str = Field(default='')
    data: T | None = Field(default=None)


class Pagination[T: BaseModel](BaseModel):
    """分页数据"""
    records: list[T] = Field(default_factory=list)
    size: int = Field(default=10, ge=1)
    current: int = Field(default=1, ge=1)
    total: int = Field(default=0)
