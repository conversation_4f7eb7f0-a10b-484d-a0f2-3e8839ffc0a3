import logging

from fastapi import APIRouter, Depends, BackgroundTasks

from models.assets import AssetType
from service.tencent.cos import client, bucket, region
from service.file import save_audio
from pydantic import BaseModel
import os
from urllib.parse import urlparse
from models.audio_education import AudioEducation
from models.users import User, get_request_user
from utils.asset_storage import store_asset_directly
from utils.database import get_db
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func
from config import app_settings
from utils.exceptions import ClientVisibleException

router = APIRouter()
logger = logging.getLogger(__name__)


class AudioReductionRequest(BaseModel):
    audio_data: str  # base64 编码的音频数据
    original_filename: str  # 音频文件的原始文件名


@router.post("/noise_reduction")
async def reduction(
        request: AudioReductionRequest,
        db: AsyncSession = Depends(get_db),
        user: User = Depends(get_request_user)
):
    try:
        # 保存并上传音频文件
        audio_url = save_audio(request.audio_data, request.original_filename)
        logger.debug(f"上传音频文件成功，文件URL: {audio_url}")
        audio_filename = os.path.basename(audio_url)

        # 提取 URL 中的路径部分
        parsed_url = urlparse(audio_url)
        audio_object_key = parsed_url.path.lstrip('/')  # 去掉路径前的 '/'
        logger.debug(f"audio_object_key:{audio_object_key}")

        job_body = {
            'Input': {
                'Object': audio_object_key
            },
            'Tag': 'NoiseReduction',
            'Operation': {
                'Output': {
                    'Region': region,
                    'Bucket': bucket,
                    'Object': f"aiadmin/audio/{os.path.splitext(audio_filename)[0]}_Reduction.wav"
                }
            }
        }
        #
        # 发起降噪任务
        response = client.ci_create_media_jobs(
            Bucket=bucket,
            Jobs=job_body,
            Lst={},
            ContentType='application/xml'
        )

        # 解析任务 ID 并返回成功结果
        job_id = response['JobsDetail'][0]['JobId']

        # 将任务信息写入数据库
        new_task = AudioEducation(
            user=user.username,
            taskid=job_id,
            status='Submitted',
            audio_url=None  # 初始时音频地址为空
        )
        db.add(new_task)
        await db.commit()
        await db.refresh(new_task)

        return {"code": "0000", "data": {"JobId": job_id}, "msg": "提交成功"}

    except Exception as e:
        # 错误处理
        logger.error(f"Error: {e}")
        raise ClientVisibleException("提交失败") from e


@router.get("/reduction_status/{job_id}")
async def reduction_status(
        job_id: str,
        background_tasks: BackgroundTasks,
        user: User = Depends(get_request_user),
        db: AsyncSession = Depends(get_db),
):
    try:
        # 发起查询任务状态的请求
        response = client.ci_get_media_jobs(
            Bucket=bucket,
            JobIDs=job_id,
            ContentType='application/xml'
        )

        # 获取任务详细信息
        job_details = response.get('JobsDetail', [])
        if not job_details:
            raise ClientVisibleException("未找到相关任务")

        job_detail = job_details[0]
        state = job_detail.get('State', 'Unknown')  # 避免KeyError
        output_audio_object = job_detail.get('Operation', {}).get('Output', {}).get('Object')

        # 使用 ORM 方法查询数据库中的任务
        result = await db.execute(
            select(AudioEducation).where(AudioEducation.taskid == job_id)
        )
        task = result.scalars().first()

        if task:
            # 更新任务状态
            task.status = state

            if state == 'Success':
                # 拼接输出音频的完整URL
                audio_url = f"{app_settings.tencent_cos_host}{output_audio_object}" if output_audio_object else None
                if audio_url and task.audio_url != audio_url:
                    input_url = app_settings.tencent_cos_host + job_detail.get('Input', {}).get('Object', '')
                    background_tasks.add_task(
                        store_asset_directly,
                        db=db,
                        asset_type=AssetType.AUDIO,
                        url=audio_url,
                        user_id=user.id,
                        biz_id='audio_noise_reduction',
                        parameter={"audio_url": input_url},
                    )
                task.audio_url = audio_url

            elif state == 'Failed':
                task.audio_url = None

            await db.commit()

            # 格式化 create_time 和 update_time，只保留到秒
            """
            uptime 字段的值是一个 datetime 对象
            而在返回给前端时，FastAPI 默认会将 datetime 对象序列化为 ISO 8601 格式的字符串
            这包括了微秒部分（如 2024-09-25 11:16:08.322424）
            """
            create_time_str = task.create_time.strftime("%Y-%m-%d %H:%M:%S") if task.create_time else None
            update_time_str = task.uptime.strftime("%Y-%m-%d %H:%M:%S") if task.uptime else None

            # 返回固定的结果，包括 state, audio, create_time, update_time
            return {
                "code": "0000",
                "data": {
                    "state": task.status,
                    "audio": task.audio_url,
                    "create_time": create_time_str,
                    "update_time": update_time_str
                },
                "msg": "查询成功"
            }

        else:
            raise ClientVisibleException("任务不存在")

    except Exception as e:
        logger.error(f"Error: {e}")
        raise ClientVisibleException("查询任务状态失败") from e


@router.get("/history")
async def get_history(
        page: int,
        size: int = 10,
        db: AsyncSession = Depends(get_db),
        user: User = Depends(get_request_user)
):
    try:
        offset = (page - 1) * size

        # 查询用户的历史记录，按创建时间降序排序
        result = await db.execute(
            select(
                AudioEducation.taskid,
                AudioEducation.status,
                AudioEducation.create_time,
                AudioEducation.uptime,
                AudioEducation.audio_url
            ).where(AudioEducation.user == user.username)
            .order_by(AudioEducation.create_time.desc())  # 按创建时间降序排序
            .offset(offset).limit(size)
        )
        history = [dict(row._mapping) for row in result]  # 使用 _mapping 转换为字典

        # 查询总记录数
        total_count_result = await db.execute(
            select(func.count()).select_from(AudioEducation).where(AudioEducation.user == user.username)
        )
        total_count = total_count_result.scalar()

        # 计算总页数
        total_pages = (total_count + size - 1) // size

        return {
            "code": "0000",
            "data": {
                "history": history,
                "total_pages": total_pages
            },
            "msg": ""
        }
    except Exception as e:
        logger.error(f"Error: {e}")
        raise ClientVisibleException("查询历史记录失败") from e
