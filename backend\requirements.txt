aiohttp==3.9.5
aiosignal==1.3.1
annotated-types==0.6.0
anthropic==0.25.6
anyio==4.5.0
asgi-user-agents==0.2.0
asttokens==2.4.1
async-timeout==4.0.3
attrs==23.2.0
backcall==0.2.0
beautifulsoup4==4.12.3
bleach==6.1.0
cachetools==5.3.3
certifi==2024.2.2
cffi==1.16.0
charset-normalizer==3.3.2
click==8.1.7
colorama==0.4.6
crcmod==1.7
cryptography==42.0.5
decorator==5.1.1
defusedxml==0.7.1
distro==1.9.0
docopt==0.6.2
docstring_parser==0.16
executing==2.0.1
fastmcp==2.8.0
fastapi==0.112.2
fastapi-limiter==0.1.6
fastjsonschema==2.19.1
filelock==3.13.4
frozenlist==1.4.1
fsspec==2024.3.1
google-api-core==2.18.0
google-auth==2.29.0
google-cloud-aiplatform==1.48.0
google-cloud-bigquery==3.21.0
google-cloud-core==2.4.1
google-cloud-resource-manager==1.12.3
google-cloud-storage==2.16.0
google-crc32c==1.5.0
google-resumable-media==2.7.0
googleapis-common-protos==1.63.0
greenlet==3.0.3
grpc-google-iam-v1==0.13.0
grpcio==1.62.2
grpcio-status==1.62.2
h11==0.14.0
httpcore==1.0.5
httpx==0.28.1
huggingface-hub==0.22.2
idna==3.7
ipython==8.12.3
jedi==0.19.1
Jinja2==3.1.3
jsonschema==4.21.1
jsonschema-specifications==2023.12.1
jupyter_client==8.6.1
jupyter_core==5.7.2
jupyterlab_pygments==0.3.0
load-dotenv==0.1.0
MarkupSafe==2.1.5
matplotlib-inline==0.1.7
mistune==3.0.2
multidict==6.0.5
nanoid==2.0.0
nbclient==0.10.0
nbconvert==7.16.3
nbformat==5.10.4
numpy==1.26.4
openai==1.66.3
packaging==24.0
pandocfilters==1.5.1
parso==0.8.4
passlib==1.7.4
pickleshare==0.7.5
pillow==10.3.0
pipreqs==0.5.0
platformdirs==4.2.1
prompt-toolkit==3.0.43
proto-plus==1.23.0
protobuf==4.25.3
pure-eval==0.2.2
pyasn1==0.6.0
pyasn1_modules==0.4.0
pycparser==2.22
pycryptodome==3.9.9
pydantic>=1.9.0,<3.0.0
pydantic-settings==2.9.1
pydantic_core==2.20.1
Pygments==2.17.2
PyMySQL==1.1.0
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-multipart==0.0.9
PyYAML==6.0.1
pyzmq==26.0.2
referencing==0.35.0
requests==2.31.0
rpds-py==0.18.0
rsa==4.9
shapely==2.0.4
six==1.16.0
sniffio==1.3.1
soupsieve==2.5
SQLAlchemy==2.0.32
stack-data==0.6.3
starlette==0.37.2
tinycss2==1.3.0
tokenizers==0.19.1
tornado==6.4
tqdm==4.66.2
traitlets==5.14.3
typing_extensions==4.12.2
urllib3==2.2.1
uvicorn==0.30.6
wcwidth==0.2.13
webencodings==0.5.1
xmltodict==0.13.0
yarg==0.1.9
yarl==1.9.4
email_validator==2.1.1
alibabacloud_dingtalk==2.1.13
pypinyin==0.51.0
aiomysql==0.2.0
langchain>=0.2.13,<0.3.0
openpyxl==3.1.0
tiktoken==0.7.0
dashscope==1.20.1
rembg==2.0.57
ffmpeg-python==0.2.0
pandas==2.2.0
# python-magic==0.4.27 # win python-magic-bin==0.4.14
python-magic-bin==0.4.14; sys_platform == 'win32'
python-magic==0.4.27; sys_platform != 'win32'
pydash==8.0.3
file_read_backwards==3.1.0
aiomysql==0.2.0
redis==5.0.8
langchain_openai==0.1.22
langchain_community==0.2.12
sse_starlette==2.1.3
ffmpeg-python==0.2.0
cos-python-sdk-v5==1.9.31
aiofiles==23.2.1
sse-starlette==2.1.3
setuptools==65.5.0
volcengine-python-sdk==1.1.5
volcengine==1.0.181
tencentcloud-sdk-python==3.0.1370
tencentcloud-sdk-python-captcha==3.0.1406
zhipuai==2.1.5.20250526
