"""
通用资产存储功能模块

使用示例：

基本用法：
```python
from utils.asset_storage import store_asset_from_instance

# 存储单个资产
asset = await store_asset_from_instance(mj_task, user, db)
```

批量存储：
```python
from utils.asset_storage import store_assets_from_instances

# 批量存储资产
assets = await store_assets_from_instances(tasks, user, db)
```

高级用法：
```python
from utils.asset_storage import get_asset_storage_manager

manager = get_asset_storage_manager()
asset = await manager.store_asset(task, user, db, force_collect=True)
```
"""
import json
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Union, Type, Tuple
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy import select

from models.assets import Assets, AssetType
from models.tasks import Task
from models.chat_mj_tasks import ChatMjTasks
from models.users import User
from utils.database import SessionLocal

# 配置日志
logger = logging.getLogger(__name__)

# 类型别名定义
TableInstance = Union[Task, ChatMjTasks, Any]  # 支持的表实例类型
ExtractedData = Dict[str, Any]  # 提取的数据类型，包含type、urls、user_id、parameter和可选的taskid

# 字段映射配置字典
FIELD_MAPPING_CONFIG = {
    # 通用任务表 (Task) 的字段映射
    "tasks": {
        "table_class": "Task",
        "fields": {
            "url": ["resource_url", "prompt_media_url"],  # 优先级顺序
            "prompt": ["task_params.prompt"],  # 支持嵌套字段访问
            "prompt_en": ["task_params.prompt_en"],
            "model": ["model"],
            "manufacturer": ["task_params.manufacturer"],
            "additional_params": [
                "task_params", "task_result", "action", "task_type"
            ]
        },
        "asset_type_mapping": {
            "image": AssetType.IMAGE,
            "video": AssetType.VIDEO,
            "audio": AssetType.AUDIO,
            "music": AssetType.AUDIO,
            "default": AssetType.IMAGE
        },
        "url_fields": {
            "single_url": "resource_url",
            "multiple_urls": "task_result.urls",  # 如果存在多个URL
            "prompt_media": "prompt_media_url"
        }
    },
    
    # MidJourney任务表 (ChatMjTasks) 的字段映射
    "chat_mj_tasks": {
        "table_class": "ChatMjTasks",
        "fields": {
            "url": ["image_url"],
            "urls": ["image_urls"],  # 多张图片URL字段
            "prompt": ["prompt"],
            "prompt_en": ["prompt_en"],
            "model": ["model"],
            "manufacturer": ["manufacturer"],
            "additional_params": [
                "action", "status", "progress", "seed", "buttons", 
                "channels", "description", "prompt_img"
            ]
        },
        "asset_type_mapping": {
            "default": AssetType.IMAGE
        },
        "url_fields": {
            "single_url": "image_url",
            "multiple_urls": "image_urls"
        }
    },
    
    # 自定义表结构的默认映射（可扩展）
    "default": {
        "table_class": "Unknown",
        "fields": {
            "url": ["url", "image_url", "resource_url"],
            "urls": ["urls", "image_urls"],
            "prompt": ["prompt", "description"],
            "prompt_en": ["prompt_en", "description_en"],
            "model": ["model", "model_name"],
            "manufacturer": ["manufacturer", "provider", "source"],
            "additional_params": []
        },
        "asset_type_mapping": {
            "default": AssetType.IMAGE
        },
        "url_fields": {
            "single_url": ["url", "image_url", "resource_url"],
            "multiple_urls": ["urls", "image_urls"],
            "prompt_media": ["prompt_img", "prompt_media"]
        }
    }
}

# URL处理配置
URL_PROCESSING_CONFIG = {
    "single_to_array": True,  # 是否将单个URL转换为数组
    "validate_urls": True,    # 是否验证URL有效性
    "supported_extensions": [".jpg", ".jpeg", ".png", ".gif", ".webp", ".mp4", ".avi", ".mov", ".mp3", ".wav"],
    "max_urls_per_asset": 10  # 每个资产最大URL数量
}

class DataExtractorStrategy(ABC):
    """
    数据提取策略抽象基类
    
    定义了从不同表结构中提取数据的标准接口，
    每个具体的表结构都应该实现这个接口。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化策略实例
        
        Args:
            config: 字段映射配置
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    async def extract_data(self, instance: TableInstance, user: User) -> ExtractedData:
        """
        从表实例中提取数据
        
        Args:
            instance: 数据表实例
            user: 用户实例
            
        Returns:
            ExtractedData: 提取的数据字典
            
        Raises:
            ValueError: 数据提取失败时抛出
        """
        pass
    
    @abstractmethod
    def get_asset_type(self, instance: TableInstance) -> AssetType:
        """
        确定资产类型
        
        Args:
            instance: 数据表实例
            
        Returns:
            AssetType: 资产类型枚举值
        """
        pass
    
    @abstractmethod
    def get_urls(self, instance: TableInstance) -> List[str]:
        """
        提取URL列表
        
        Args:
            instance: 数据表实例
            
        Returns:
            List[str]: URL列表
        """
        pass
    
    def _safe_get_nested_attr(self, obj: Any, attr_path: str, default: Any = None) -> Any:
        """
        安全获取嵌套属性值
        
        Args:
            obj: 对象实例
            attr_path: 属性路径，如 'task_params.prompt'
            default: 默认值
            
        Returns:
            Any: 属性值或默认值
        """
        try:
            attrs = attr_path.split('.')
            current = obj
            
            for attr in attrs:
                if hasattr(current, attr):
                    current = getattr(current, attr)
                elif isinstance(current, dict) and attr in current:
                    current = current[attr]
                else:
                    return default
                    
            return current
        except (AttributeError, KeyError, TypeError):
            return default
    
    def _extract_field_value(self, instance: TableInstance, field_configs: List[str]) -> Any:
        """
        根据字段配置提取值
        
        Args:
            instance: 数据表实例
            field_configs: 字段配置列表（按优先级排序）
            
        Returns:
            Any: 提取的值，如果都不存在则返回None
        """
        for field_config in field_configs:
            value = self._safe_get_nested_attr(instance, field_config)
            if value is not None:
                return value
        return None

class TaskDataExtractor(DataExtractorStrategy):
    """
    通用任务表(Task)数据提取策略
    
    专门用于从Task表中提取资产相关数据
    """
    
    async def extract_data(self, instance: Task, user: User) -> ExtractedData:
        """
        从Task实例中提取数据
        
        Args:
            instance: Task实例
            user: 用户实例
            
        Returns:
            ExtractedData: 提取的数据字典
        """
        try:
            fields_config = self.config["fields"]
            
            # 提取基础字段
            prompt = self._extract_field_value(instance, fields_config.get("prompt", []))
            prompt_en = self._extract_field_value(instance, fields_config.get("prompt_en", []))
            model = self._extract_field_value(instance, fields_config.get("model", []))
            manufacturer = self._extract_field_value(instance, fields_config.get("manufacturer", []))
            
            # 提取taskid作为独立字段
            taskid = getattr(instance, "taskid", None)
            
            # 构建生成参数（不再包含taskid）
            parameters = {
                "prompt": prompt,
                "prompt_en": prompt_en,
                "model": model,
                "manufacturer": manufacturer,
                "task_type": getattr(instance, "task_type", None),
                "action": getattr(instance, "action", None),
                "submit_time": getattr(instance, "submit_time", None),
                "finish_time": getattr(instance, "finish_time", None)
            }
            
            # 添加额外参数
            for param_field in fields_config.get("additional_params", []):
                param_value = self._safe_get_nested_attr(instance, param_field)
                if param_value is not None:
                    # 处理嵌套参数的键名
                    param_key = param_field.split('.')[-1]
                    parameters[param_key] = param_value
            
            # 过滤空值
            parameters = {k: v for k, v in parameters.items() if v is not None}
            
            # 清理参数数据，处理datetime对象和嵌套结构
            try:
                cleaned_parameters = clean_parameter_data(parameters)
                self.logger.debug(f"参数清理完成，原始参数数量: {len(parameters)}, 清理后参数数量: {len(cleaned_parameters)}")
            except Exception as e:
                self.logger.warning(f"参数清理失败，使用原始参数: {str(e)}")
                cleaned_parameters = {k: str(v) if v is not None else None for k, v in parameters.items()}
            
            extracted_data = {
                "type": self.get_asset_type(instance),
                "urls": self.get_urls(instance),
                "user_id": user.id,
                "parameter": cleaned_parameters,
                "taskid": taskid
            }
            
            self.logger.info(f"成功从Task(id={instance.id})中提取数据")
            return extracted_data
            
        except Exception as e:
            self.logger.error(f"从Task实例提取数据失败: {str(e)}")
            raise ValueError(f"数据提取失败: {str(e)}")
    
    def get_asset_type(self, instance: Task) -> AssetType:
        """
        根据任务类型确定资产类型
        
        Args:
            instance: Task实例
            
        Returns:
            AssetType: 资产类型
        """
        task_type = getattr(instance, "task_type", "").lower()
        asset_type_mapping = self.config.get("asset_type_mapping", {})
        return asset_type_mapping.get(task_type, asset_type_mapping.get("default", AssetType.IMAGE))
    
    def get_urls(self, instance: Task) -> List[str]:
        """
        从Task实例中提取URL列表
        
        对于视频任务，只提取生成的视频URL，不包含参考图片(prompt_media_url)：
        - 避免将输入的参考图片作为生成资产存储
        - 保持与ChatMjTasksDataExtractor的一致性设计理念
        
        Args:
            instance: Task实例
            
        Returns:
            List[str]: URL列表（对于视频任务仅包含生成的视频URL）
        """
        urls = []
        url_fields = self.config.get("url_fields", {})
        task_type = getattr(instance, "task_type", "").lower()
        
        # 检查单个URL字段（生成的资源URL）
        single_url_field = url_fields.get("single_url")
        if single_url_field:
            single_url = self._safe_get_nested_attr(instance, single_url_field)
            if single_url and isinstance(single_url, str):
                urls.append(single_url)
        
        # 检查多个URL字段  
        multiple_urls_field = url_fields.get("multiple_urls")
        if multiple_urls_field:
            multiple_urls = self._safe_get_nested_attr(instance, multiple_urls_field)
            if multiple_urls:
                if isinstance(multiple_urls, str):
                    # 如果是字符串，尝试解析为列表
                    try:
                        import json
                        parsed_urls = json.loads(multiple_urls)
                        if isinstance(parsed_urls, list):
                            urls.extend(parsed_urls)
                    except:
                        urls.append(multiple_urls)
                elif isinstance(multiple_urls, list):
                    urls.extend(multiple_urls)
        
        # 检查prompt媒体URL - 对于视频任务跳过此步骤，避免重复存储参考图片
        if task_type != "video":
            prompt_media_field = url_fields.get("prompt_media")
            if prompt_media_field:
                prompt_media_url = self._safe_get_nested_attr(instance, prompt_media_field)
                if prompt_media_url and isinstance(prompt_media_url, str):
                    urls.append(prompt_media_url)
        
        # 去重并过滤空值
        unique_urls = list(set(url for url in urls if url and url.strip()))
        
        self.logger.debug(f"从Task实例提取到{len(unique_urls)}个URL（任务类型：{task_type}）")
        return unique_urls

class ChatMjTasksDataExtractor(DataExtractorStrategy):
    """
    MidJourney任务表(ChatMjTasks)数据提取策略
    
    专门用于从ChatMjTasks表中提取资产相关数据
    """
    
    async def extract_data(self, instance: ChatMjTasks, user: User) -> ExtractedData:
        """
        从ChatMjTasks实例中提取数据
        
        Args:
            instance: ChatMjTasks实例
            user: 用户实例
            
        Returns:
            ExtractedData: 提取的数据字典
        """
        try:
            fields_config = self.config["fields"]
            
            # 提取基础字段
            prompt = self._extract_field_value(instance, fields_config.get("prompt", []))
            prompt_en = self._extract_field_value(instance, fields_config.get("prompt_en", []))
            model = self._extract_field_value(instance, fields_config.get("model", []))
            manufacturer = self._extract_field_value(instance, fields_config.get("manufacturer", []))
            
            # 提取taskid作为独立字段
            taskid = getattr(instance, "taskid", None)
            
            # 构建生成参数（不再包含taskid）
            parameters = {
                "prompt": prompt,
                "prompt_en": prompt_en,
                "model": model,
                "manufacturer": manufacturer.value if hasattr(manufacturer, 'value') else manufacturer,
                "action": getattr(instance, "action", None).value if hasattr(getattr(instance, "action", None), 'value') else getattr(instance, "action", None),
                "status": getattr(instance, "status", None).value if hasattr(getattr(instance, "status", None), 'value') else getattr(instance, "status", None),
                "submit_time": getattr(instance, "submit_time", None),
                "finish_time": getattr(instance, "finish_time", None),
                "progress": getattr(instance, "progress", None),
                "seed": getattr(instance, "seed", None),
                "channels": getattr(instance, "channels", None),
                "description": getattr(instance, "description", None)
            }
            
            # 添加额外参数
            for param_field in fields_config.get("additional_params", []):
                param_value = self._safe_get_nested_attr(instance, param_field)
                if param_value is not None:
                    param_key = param_field.split('.')[-1]
                    # 处理枚举类型
                    if hasattr(param_value, 'value'):
                        param_value = param_value.value
                    parameters[param_key] = param_value
            
            # 处理特殊字段
            if hasattr(instance, "buttons") and instance.buttons:
                parameters["buttons"] = instance.buttons
            if hasattr(instance, "button") and instance.button:
                parameters["button"] = instance.button
            
            # 过滤空值
            parameters = {k: v for k, v in parameters.items() if v is not None}
            
            # 清理参数数据，处理datetime对象和枚举类型的序列化
            try:
                cleaned_parameters = clean_parameter_data(parameters)
                self.logger.debug(f"参数清理完成，原始参数数量: {len(parameters)}, 清理后参数数量: {len(cleaned_parameters)}")
            except Exception as e:
                self.logger.warning(f"参数清理失败，使用原始参数: {str(e)}")
                cleaned_parameters = {k: str(v) if v is not None else None for k, v in parameters.items()}
            
            extracted_data = {
                "type": self.get_asset_type(instance),
                "urls": self.get_urls(instance),
                "user_id": user.id,
                "parameter": cleaned_parameters,
                "taskid": taskid
            }
            
            self.logger.info(f"成功从ChatMjTasks(id={instance.id})中提取数据，URL数量: {len(extracted_data['urls'])}")
            return extracted_data
            
        except Exception as e:
            self.logger.error(f"从ChatMjTasks实例提取数据失败: {str(e)}")
            raise ValueError(f"数据提取失败: {str(e)}")
    
    def get_asset_type(self, instance: ChatMjTasks) -> AssetType:
        """
        确定资产类型（MidJourney主要生成图片）
        
        Args:
            instance: ChatMjTasks实例
            
        Returns:
            AssetType: 资产类型（默认为图片）
        """
        return AssetType.IMAGE
    
    def get_urls(self, instance: ChatMjTasks) -> List[str]:
        """
        从ChatMjTasks实例中提取URL列表
        
        只提取生成的图片URL，不包含参考图片(prompt_img)：
        - 如果image_urls不为空，直接使用
        - 如果image_urls为空且image_url不为空，将单个URL转换为数组
        
        Args:
            instance: ChatMjTasks实例
            
        Returns:
            List[str]: URL列表（仅包含生成的图片URL）
        """
        urls = []
        
        # 优先检查多个URL字段 (image_urls)
        image_urls = getattr(instance, "image_urls", None)
        if image_urls:
            if isinstance(image_urls, str):
                # 如果是字符串，尝试解析为JSON列表
                try:
                    import json
                    parsed_urls = json.loads(image_urls)
                    if isinstance(parsed_urls, list):
                        urls.extend(parsed_urls)
                    else:
                        urls.append(image_urls)
                except json.JSONDecodeError:
                    # 如果不是有效JSON，按逗号分割
                    urls.extend([url.strip() for url in image_urls.split(',') if url.strip()])
            elif isinstance(image_urls, list):
                urls.extend(image_urls)
        
        # 如果没有多个URL，检查单个URL字段 (image_url)
        if not urls:
            image_url = getattr(instance, "image_url", None)
            if image_url and isinstance(image_url, str):
                urls.append(image_url)
        
        # prompt_img信息仍会保存在参数中，供其他功能使用
        
        # 去重并过滤空值
        unique_urls = list(set(url for url in urls if url and url.strip()))
        
        self.logger.debug(f"从ChatMjTasks实例提取到{len(unique_urls)}个URL（仅生成图片）")
        return unique_urls

class URLProcessor:
    """
    URL处理器
    
    负责URL的格式转换、验证和处理
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化URL处理器
        
        Args:
            config: URL处理配置
        """
        self.config = config or URL_PROCESSING_CONFIG
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def process_urls(self, urls: List[str]) -> List[str]:
        """
        处理URL列表
        
        Args:
            urls: 原始URL列表
            
        Returns:
            List[str]: 处理后的URL列表
        """
        if not urls:
            return []
        
        processed_urls = []
        
        for url in urls:
            if self._is_valid_url(url):
                processed_url = self._normalize_url(url)
                if processed_url:
                    processed_urls.append(processed_url)
        
        # 去重
        unique_urls = list(dict.fromkeys(processed_urls))
        
        # 限制URL数量
        max_urls = self.config.get("max_urls_per_asset", 10)
        if len(unique_urls) > max_urls:
            self.logger.warning(f"URL数量超过限制({max_urls})，将截取前{max_urls}个")
            unique_urls = unique_urls[:max_urls]
        
        self.logger.debug(f"处理URL: {len(urls)} -> {len(unique_urls)}")
        return unique_urls
    
    def _is_valid_url(self, url: str) -> bool:
        """
        验证URL有效性
        
        Args:
            url: URL字符串
            
        Returns:
            bool: 是否有效
        """
        if not url or not isinstance(url, str):
            return False
        
        url = url.strip()
        if not url:
            return False
        
        # 基本URL格式验证
        if not (url.startswith('http://') or url.startswith('https://')):
            return False
        
        # 验证文件扩展名（如果启用）
        if self.config.get("validate_urls", True):
            supported_extensions = self.config.get("supported_extensions", [])
            if supported_extensions:
                url_lower = url.lower()
                # 移除查询参数进行扩展名检查
                clean_url = url_lower.split('?')[0]
                if not any(clean_url.endswith(ext.lower()) for ext in supported_extensions):
                    # 如果没有明确的文件扩展名，假设是有效的（可能是动态URL）
                    if '.' in clean_url.split('/')[-1]:
                        return False
        
        return True
    
    def _normalize_url(self, url: str) -> str:
        """
        标准化URL
        
        Args:
            url: 原始URL
            
        Returns:
            str: 标准化后的URL
        """
        if not url:
            return ""
        
        # 去除首尾空格
        url = url.strip()
        
        # 确保URL编码正确
        try:
            from urllib.parse import quote, unquote
            # 先解码再编码，处理可能的重复编码
            decoded_url = unquote(url)
            # 对URL路径部分进行编码（保留协议和域名部分）
            parts = decoded_url.split('/', 3)
            if len(parts) > 3:
                # 只对路径部分进行编码
                encoded_path = quote(parts[3], safe='/?&=')
                url = '/'.join(parts[:3]) + '/' + encoded_path
        except Exception as e:
            self.logger.warning(f"URL标准化失败: {url}, 错误: {str(e)}")
        
        return url
    
    def convert_single_to_array(self, url: str) -> List[str]:
        """
        将单个URL转换为数组格式
        
        Args:
            url: 单个URL
            
        Returns:
            List[str]: URL数组
        """
        if not url:
            return []
        
        if self.config.get("single_to_array", True):
            return self.process_urls([url])
        else:
            return [url] if self._is_valid_url(url) else []

class AssetStorageManager:
    """
    资产存储管理器
    
    提供统一的资产存储接口，负责协调各个组件完成资产存储流程
    """
    
    def __init__(self):
        """
        初始化资产存储管理器
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.url_processor = URLProcessor()
        self._strategies = self._initialize_strategies()
    
    def _initialize_strategies(self) -> Dict[str, DataExtractorStrategy]:
        """
        初始化数据提取策略
        
        Returns:
            Dict[str, DataExtractorStrategy]: 策略映射字典
        """
        strategies = {}
        
        # 注册Task策略
        strategies["Task"] = TaskDataExtractor(FIELD_MAPPING_CONFIG["tasks"])
        strategies["tasks"] = TaskDataExtractor(FIELD_MAPPING_CONFIG["tasks"])
        
        # 注册ChatMjTasks策略
        strategies["ChatMjTasks"] = ChatMjTasksDataExtractor(FIELD_MAPPING_CONFIG["chat_mj_tasks"])
        strategies["chat_mj_tasks"] = ChatMjTasksDataExtractor(FIELD_MAPPING_CONFIG["chat_mj_tasks"])
        
        self.logger.info(f"初始化完成，注册了{len(strategies)}个数据提取策略")
        return strategies
    
    def _get_strategy(self, instance: TableInstance) -> DataExtractorStrategy:
        """
        根据实例类型获取对应的数据提取策略
        
        Args:
            instance: 数据表实例
            
        Returns:
            DataExtractorStrategy: 对应的数据提取策略
            
        Raises:
            ValueError: 没有找到对应策略时抛出
        """
        class_name = instance.__class__.__name__
        table_name = getattr(instance, '__tablename__', '').lower()
        
        # 优先根据类名查找
        if class_name in self._strategies:
            return self._strategies[class_name]
        
        # 根据表名查找
        if table_name in self._strategies:
            return self._strategies[table_name]
        
        # 使用默认策略（基于Task的通用策略）
        self.logger.warning(f"未找到{class_name}({table_name})的专用策略，使用默认策略")
        return TaskDataExtractor(FIELD_MAPPING_CONFIG["default"])
    
    async def store_asset(
        self, 
        instance: TableInstance, 
        user: User, 
        db: AsyncSession,
        force_collect: bool = False
    ) -> Optional[Assets]:
        """
        存储资产
        
        Args:
            instance: 数据表实例（如Task、ChatMjTasks等）
            user: 用户实例
            db: 数据库会话
            force_collect: 是否强制设置为收藏状态
            
        Returns:
            Optional[Assets]: 创建的资产实例，失败时返回None
            
        Raises:
            ValueError: 数据提取或存储失败时抛出
        """
        try:
            # 获取对应的数据提取策略
            strategy = self._get_strategy(instance)
            
            # 提取数据
            extracted_data = await strategy.extract_data(instance, user)
            
            # 处理URL
            raw_urls = extracted_data.get("urls", [])
            processed_urls = self.url_processor.process_urls(raw_urls)
            
            if not processed_urls:
                self.logger.warning(f"实例{instance.__class__.__name__}(id={getattr(instance, 'id', 'Unknown')})没有有效的URL，跳过资产存储")
                return None
            
            # 创建资产记录
            assets = []
            for url in processed_urls:
                asset = Assets(
                    type=extracted_data["type"],
                    url=url,
                    user_id=extracted_data["user_id"],
                    create_time=datetime.now(),
                    iscollect=1 if force_collect else 0,
                    parameter=extracted_data["parameter"],
                    taskid=extracted_data.get("taskid")
                )
                assets.append(asset)
                db.add(asset)
            
            # 提交数据库
            await db.commit()
            
            # 刷新实例
            for asset in assets:
                await db.refresh(asset)
            
            self.logger.info(f"成功存储{len(assets)}个资产记录")
            
            # 返回第一个资产（如果有多个URL，返回主要的那个）
            return assets[0] if assets else None
            
        except Exception as e:
            await db.rollback()
            self.logger.error(f"存储资产失败: {str(e)}")
            raise ValueError(f"存储资产失败: {str(e)}")
    
    async def store_multiple_assets(
        self, 
        instances: List[TableInstance], 
        user: User, 
        db: AsyncSession,
        force_collect: bool = False
    ) -> List[Assets]:
        """
        批量存储资产
        
        Args:
            instances: 数据表实例列表
            user: 用户实例
            db: 数据库会话
            force_collect: 是否强制设置为收藏状态
            
        Returns:
            List[Assets]: 创建的资产列表
        """
        stored_assets = []
        
        for instance in instances:
            try:
                asset = await self.store_asset(instance, user, db, force_collect)
                if asset:
                    stored_assets.append(asset)
            except Exception as e:
                self.logger.error(f"批量存储中单个实例失败: {str(e)}")
                continue
        
        self.logger.info(f"批量存储完成，成功存储{len(stored_assets)}个资产")
        return stored_assets
    
    def register_strategy(self, key: str, strategy: DataExtractorStrategy):
        """
        注册新的数据提取策略
        
        Args:
            key: 策略键名（通常是表名或类名）
            strategy: 数据提取策略实例
        """
        self._strategies[key] = strategy
        self.logger.info(f"注册新策略: {key}")
    
    def get_supported_tables(self) -> List[str]:
        """
        获取支持的表类型列表
        
        Returns:
            List[str]: 支持的表类型列表
        """
        return list(self._strategies.keys())
    
    async def delete_assets_by_taskid(
        self,
        db: AsyncSession,
        taskid: str,
        user_id: int = None
    ) -> Tuple[bool, int, Optional[str]]:
        """
        根据taskid删除相关的资产数据
        
        Args:
            db: 数据库会话
            taskid: 任务ID
            user_id: 用户ID（可选，用于额外的安全检查）
            
        Returns:
            Tuple[success, deleted_count, error_message]: 删除是否成功、删除的资产数量和错误信息
        """
        try:
            from sqlalchemy import and_
            from models.assets import Assets
            
            # 构建查询条件：查找taskid字段匹配的资产
            query = select(Assets).filter(Assets.taskid == taskid)
            
            # 如果提供了user_id，添加额外的安全检查
            if user_id is not None:
                query = query.filter(Assets.user_id == user_id)
            
            # 执行查询获取需要删除的资产
            result = await db.execute(query)
            assets_to_delete = result.scalars().all()
            
            if not assets_to_delete:
                self.logger.info(f"未找到taskid为{taskid}的相关资产")
                return True, 0, None
            
            # 记录将要删除的资产信息
            asset_info = []
            for asset in assets_to_delete:
                asset_info.append({
                    "id": asset.id,
                    "type": asset.type,
                    "url": asset.url[:100],  # 截取URL前100个字符用于日志
                    "user_id": asset.user_id
                })
            
            self.logger.info(f"准备删除taskid为{taskid}的{len(assets_to_delete)}个资产: {asset_info}")
            
            # 执行批量删除
            for asset in assets_to_delete:
                await db.delete(asset)
            
            # 这里不提交事务，由调用方统一提交
            # 这样可以确保任务删除和资产删除在同一个事务中
            
            deleted_count = len(assets_to_delete)
            self.logger.info(f"成功删除taskid为{taskid}的{deleted_count}个资产")
            
            return True, deleted_count, None
            
        except Exception as e:
            self.logger.error(f"删除taskid为{taskid}的资产时出错: {str(e)}", exc_info=True)
            return False, 0, f"删除资产时出错"

# 辅助工具函数

def safe_json_loads(data: Any, default: Any = None) -> Any:
    """
    安全地解析JSON数据
    
    Args:
        data: 待解析的数据
        default: 解析失败时的默认值
        
    Returns:
        Any: 解析结果或默认值
    """
    if data is None:
        return default
    
    if isinstance(data, (dict, list)):
        return data
    
    if isinstance(data, str):
        try:
            import json
            return json.loads(data)
        except (json.JSONDecodeError, ValueError):
            return default
    
    return default

def convert_datetime_to_string(dt: Optional[datetime]) -> Optional[str]:
    """
    将datetime对象转换为字符串
    
    Args:
        dt: datetime对象
        
    Returns:
        Optional[str]: 格式化的时间字符串
    """
    if dt is None:
        return None
    
    if isinstance(dt, datetime):
        return dt.strftime("%Y-%m-%d %H:%M:%S")
    
    return str(dt)

def normalize_enum_value(value: Any) -> Any:
    """
    标准化枚举值
    
    Args:
        value: 枚举值或其他类型的值
        
    Returns:
        Any: 标准化后的值
    """
    if value is None:
        return None
    
    # 如果是枚举类型，返回其值
    if hasattr(value, 'value'):
        return value.value
    
    return value

def validate_asset_data(data: Dict[str, Any]) -> bool:
    """
    验证资产数据的完整性
    
    Args:
        data: 资产数据字典
        
    Returns:
        bool: 验证是否通过
    """
    required_fields = ["type", "urls", "user_id"]
    
    for field in required_fields:
        if field not in data or data[field] is None:
            return False
    
    # 验证URL列表不为空
    if not data["urls"] or not isinstance(data["urls"], list):
        return False
    
    # 验证用户ID为正整数
    if not isinstance(data["user_id"], int) or data["user_id"] <= 0:
        return False
    
    return True

def clean_parameter_data(parameters: Dict[str, Any]) -> Dict[str, Any]:
    """
    清理参数数据，移除空值和无效值，支持递归处理嵌套数据结构
    
    Args:
        parameters: 原始参数字典
        
    Returns:
        Dict[str, Any]: 清理后的参数字典
    """
    if not parameters or not isinstance(parameters, dict):
        return {}
    
    cleaned = {}
    
    for key, value in parameters.items():
        if value is None:
            continue
        
        # 递归处理嵌套字典
        if isinstance(value, dict):
            cleaned_nested = clean_parameter_data(value)
            if cleaned_nested:  # 只添加非空的嵌套字典
                cleaned[key] = cleaned_nested
            continue
        
        # 递归处理列表
        if isinstance(value, list):
            cleaned_list = []
            for item in value:
                if isinstance(item, dict):
                    cleaned_item = clean_parameter_data(item)
                    if cleaned_item:
                        cleaned_list.append(cleaned_item)
                elif isinstance(item, datetime):
                    cleaned_list.append(convert_datetime_to_string(item))
                elif item is not None:
                    # 处理枚举类型和其他类型
                    cleaned_item = normalize_enum_value(item)
                    if cleaned_item is not None:
                        cleaned_list.append(cleaned_item)
            if cleaned_list:  # 只添加非空的列表
                cleaned[key] = cleaned_list
            continue
        
        # 处理字符串
        if isinstance(value, str):
            value = value.strip()
            if not value:
                continue
        
        # 处理datetime对象
        if isinstance(value, datetime):
            value = convert_datetime_to_string(value)
        
        # 处理枚举类型
        value = normalize_enum_value(value)
        
        cleaned[key] = value
    
    return cleaned

# 全局实例
_asset_storage_manager: Optional[AssetStorageManager] = None

def get_asset_storage_manager() -> AssetStorageManager:
    """
    获取全局资产存储管理器实例（单例模式）
    
    Returns:
        AssetStorageManager: 资产存储管理器实例
    """
    global _asset_storage_manager
    
    if _asset_storage_manager is None:
        _asset_storage_manager = AssetStorageManager()
    
    return _asset_storage_manager

# 便捷函数

async def store_asset_from_instance(
    instance: TableInstance,
    user: User,
    db: AsyncSession,
    force_collect: bool = False
) -> Optional[Assets]:
    """
    便捷函数：从表实例存储资产
    
    Args:
        instance: 数据表实例
        user: 用户实例
        db: 数据库会话
        force_collect: 是否强制收藏
        
    Returns:
        Optional[Assets]: 创建的资产实例
    """
    manager = get_asset_storage_manager()
    return await manager.store_asset(instance, user, db, force_collect)

async def store_assets_from_instances(
    instances: List[TableInstance],
    user: User,
    db: AsyncSession,
    force_collect: bool = False
) -> List[Assets]:
    """
    便捷函数：批量从表实例存储资产
    
    Args:
        instances: 数据表实例列表
        user: 用户实例
        db: 数据库会话
        force_collect: 是否强制收藏
        
    Returns:
        List[Assets]: 创建的资产列表
    """
    manager = get_asset_storage_manager()
    return await manager.store_multiple_assets(instances, user, db, force_collect)

async def delete_assets_by_taskid_from_manager(
    db: AsyncSession,
    taskid: str,
    user_id: int = None
) -> Tuple[bool, int, Optional[str]]:
    """
    便捷函数：根据taskid删除相关资产
    
    Args:
        db: 数据库会话
        taskid: 任务ID
        user_id: 用户ID（可选，用于额外的安全检查）
        
    Returns:
        Tuple[success, deleted_count, error_message]: 删除是否成功、删除的资产数量和错误信息
    """
    manager = get_asset_storage_manager()
    return await manager.delete_assets_by_taskid(db, taskid, user_id)


async def store_asset_directly(
        db: AsyncSession,
        asset_type: AssetType,
        url: str | list[str],
        user_id: int,
        iscollect: bool = False,
        parameter: Dict[str, Any] = None,
        taskid: str = None,
        biz_id: str = '',
) -> Assets:
    if isinstance(url, list):
        url = json.dumps(url)
    asset = Assets(
        type=asset_type,
        url=url,
        user_id=user_id,
        create_time=datetime.now(),
        iscollect=1 if iscollect else 0,
        parameter=clean_parameter_data(parameter),
        taskid=taskid,
        biz_id=biz_id,
    )
    async with db as sess:
        sess.add(asset)
        await sess.commit()
        await sess.refresh(asset)
    return asset


def sync_store_asset_directly(
        asset_type: AssetType,
        url: str | list[str],
        user_id: int,
        iscollect: bool = False,
        parameter: Dict[str, Any] = None,
        taskid: str = None,
        biz_id: str = '',
) -> Assets:
    if isinstance(url, list):
        url = json.dumps(url)
    asset = Assets(
        type=asset_type,
        url=url,
        user_id=user_id,
        create_time=datetime.now(),
        iscollect=1 if iscollect else 0,
        parameter=clean_parameter_data(parameter),
        taskid=taskid,
        biz_id=biz_id,
    )
    with SessionLocal() as sess:
        sess.add(asset)
        sess.commit()
        sess.refresh(asset)
    return asset


# 异常类定义

class AssetStorageError(Exception):
    """资产存储基础异常"""
    pass

class DataExtractionError(AssetStorageError):
    """数据提取异常"""
    pass

class URLProcessingError(AssetStorageError):
    """URL处理异常"""
    pass

class StrategyNotFoundError(AssetStorageError):
    """策略未找到异常"""
    pass

class ValidationError(AssetStorageError):
    """数据验证异常"""
    pass

# 错误处理装饰器

from functools import wraps
from typing import Callable, Any

def handle_asset_storage_errors(
    default_return: Any = None,
    log_errors: bool = True,
    re_raise: bool = False
) -> Callable:
    """
    资产存储错误处理装饰器
    
    Args:
        default_return: 发生错误时的默认返回值
        log_errors: 是否记录错误日志
        re_raise: 是否重新抛出异常
        
    Returns:
        Callable: 装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except AssetStorageError as e:
                if log_errors:
                    logger.error(f"资产存储操作失败 [{func.__name__}]: {str(e)}")
                if re_raise:
                    raise
                return default_return
            except Exception as e:
                if log_errors:
                    logger.error(f"资产存储操作发生未预期错误 [{func.__name__}]: {str(e)}")
                if re_raise:
                    raise AssetStorageError(f"操作失败: {str(e)}")
                return default_return
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except AssetStorageError as e:
                if log_errors:
                    logger.error(f"资产存储操作失败 [{func.__name__}]: {str(e)}")
                if re_raise:
                    raise
                return default_return
            except Exception as e:
                if log_errors:
                    logger.error(f"资产存储操作发生未预期错误 [{func.__name__}]: {str(e)}")
                if re_raise:
                    raise AssetStorageError(f"操作失败: {str(e)}")
                return default_return
        
        # 根据函数是否为协程选择包装器
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

# 日志记录工具

class AssetStorageLogger:
    """
    资产存储专用日志记录器
    """
    
    def __init__(self, name: str = "AssetStorage"):
        self.logger = logging.getLogger(name)
    
    def log_extraction_start(self, instance_type: str, instance_id: Any):
        """记录数据提取开始"""
        self.logger.info(f"开始从{instance_type}(id={instance_id})提取数据")
    
    def log_extraction_success(self, instance_type: str, instance_id: Any, url_count: int):
        """记录数据提取成功"""
        self.logger.info(f"成功从{instance_type}(id={instance_id})提取数据，包含{url_count}个URL")
    
    def log_extraction_failure(self, instance_type: str, instance_id: Any, error: str):
        """记录数据提取失败"""
        self.logger.error(f"从{instance_type}(id={instance_id})提取数据失败: {error}")
    
    def log_url_processing(self, original_count: int, processed_count: int):
        """记录URL处理结果"""
        self.logger.debug(f"URL处理完成: {original_count} -> {processed_count}")
    
    def log_asset_creation(self, asset_count: int, user_id: int):
        """记录资产创建"""
        self.logger.info(f"为用户{user_id}成功创建{asset_count}个资产记录")
    
    def log_strategy_selection(self, instance_type: str, strategy_name: str):
        """记录策略选择"""
        self.logger.debug(f"为{instance_type}选择策略: {strategy_name}")
    
    def log_validation_failure(self, reason: str):
        """记录验证失败"""
        self.logger.warning(f"数据验证失败: {reason}")

# 性能监控

import time
from contextlib import asynccontextmanager, contextmanager

@asynccontextmanager
async def log_performance(operation_name: str, logger_instance: logging.Logger = logger):
    """
    异步性能监控上下文管理器
    
    Args:
        operation_name: 操作名称
        logger_instance: 日志实例
    """
    start_time = time.time()
    try:
        yield
    finally:
        end_time = time.time()
        duration = end_time - start_time
        logger_instance.info(f"操作 [{operation_name}] 耗时: {duration:.3f}秒")

@contextmanager
def log_sync_performance(operation_name: str, logger_instance: logging.Logger = logger):
    """
    同步性能监控上下文管理器
    
    Args:
        operation_name: 操作名称
        logger_instance: 日志实例
    """
    start_time = time.time()
    try:
        yield
    finally:
        end_time = time.time()
        duration = end_time - start_time
        logger_instance.info(f"操作 [{operation_name}] 耗时: {duration:.3f}秒")
