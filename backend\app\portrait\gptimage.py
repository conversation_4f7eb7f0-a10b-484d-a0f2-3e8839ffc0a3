import logging

from fastapi import APIRouter, Body, Depends, HTTPException, Request, status, BackgroundTasks
from typing import Annotated, Literal, Optional, List, Dict, Any
from pydantic import BaseModel, Field
from models.users import User, get_request_user
from models.chat_mj_tasks import ChatMjTasks, ChatMjTasksOut, ChatMjTasksModel, ChatMjTasksStatus, ChatMjTasksAction
from models.response import success
from sqlalchemy.orm import Session
from service.credit import CreditOperator
from utils.database import get_db, AsyncSessionLocal
from utils.common import contains_chinese, uncamelize, timestamp_to_string
from service.translate import get_translate
from utils.asset_storage import store_asset_from_instance
from utils.exceptions import ClientVisibleException
from utils.redis import redis
from urllib.parse import urljoin
import os
import requests
import json
import uuid
from datetime import timedelta, datetime
import asyncio
from task.task import add_task
from sqlalchemy.future import select
from sqlalchemy import text, func, and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError
import httpx
import time
from dotenv import load_dotenv
from utils.hash.md5 import md5
import contextvars
import threading

# 任务锁字典，用于防止并发更新同一任务
task_locks = {}
task_lock = threading.Lock()

from service.openai.image import create_image_with_gpt
from service.file import save_image_by_url, save_image_b64data
from utils.image import is_base64_image, is_url, get_image_proportion
from config import app_settings

import re
import aiohttp
import base64
import nanoid

# Redis key前缀，用于存储task的image_urls数据
REDIS_TASK_KEY_PREFIX = "gptimage:task:"
# Redis缓存过期时间（1天）
REDIS_CACHE_EXPIRY = 86400

router = APIRouter()

logger = logging.getLogger(__name__)


class GptImageRequest(BaseModel):
    prompt: str
    image_urls: List[str] = []
    taskid: str = ""
    num: int = 1
    preserve_history: bool = False  # 是否保留历史图片
    proportion: Optional[str] = None
    regenerate_index: Optional[int] = None  # 重新生成特定索引的图片


class GptImageResponse(success):
    data: Optional[ChatMjTasksOut] = None


# 获取任务的Redis键名
def get_redis_task_key(taskid):
    return f"{REDIS_TASK_KEY_PREFIX}{taskid}"


# 从Redis获取任务的image_urls数据
async def get_image_urls_from_redis(taskid):
    try:
        data = await redis.exec("get", get_redis_task_key(taskid))
        if data:
            return json.loads(data)
        return None
    except Exception as e:
        logger.error(f"从Redis获取image_urls失败: {str(e)}")
        return None


# 将任务的image_urls数据保存到Redis
async def save_image_urls_to_redis(taskid, image_urls, expiry=REDIS_CACHE_EXPIRY):
    try:
        await redis.exec("set", get_redis_task_key(taskid), json.dumps(image_urls), ex=expiry)
        logger.info(f"已将image_urls保存到Redis: {taskid}")
        return True
    except Exception as e:
        logger.error(f"保存image_urls到Redis失败: {str(e)}")
        return False


async def process_image_generation(
    prompt_en: str,
    image_urls: List[str],
    task_id: int,
    num: int,
    taskid: str,
    operator: CreditOperator,
    preserve_history: bool = False,  # 添加preserve_history参数
    proportion: Optional[str] = None,  # 添加proportion参数
):
    """
    后台任务：处理图像生成

    Args:
        prompt_en: 英文提示词
        image_urls: 图片URL列表
        task_id: 任务ID
        num: 生成图片数量
        taskid: 任务ID
        operator: 积分操作
        preserve_history: 是否保留历史图片
        proportion: 图片比例，如果为空则自动检测
    """
    # 创建或获取任务锁
    async def get_task_lock():
        global task_locks
        with task_lock:
            if task_id not in task_locks:
                task_locks[task_id] = asyncio.Lock()
            return task_locks[task_id]
    
    task_specific_lock = await get_task_lock()
    
    try:
        # 使用AsyncSessionLocal创建数据库会话
        async with AsyncSessionLocal() as db:
            # 查询任务
            result = await db.execute(select(ChatMjTasks).where(ChatMjTasks.id == task_id))
            task = result.scalars().first()

            if not task:
                logger.error(f"任务不存在: {task_id}")
                return

            try:
                # 预处理图片URL（将base64图片数据上传到OSS）
                oss_image_urls = []
                if image_urls:
                    logger.info(f"开始处理输入图片，数量: {len(image_urls)}")
                    for img_data in image_urls:
                        try:
                            # 检查是否为有效数据
                            if not img_data or not isinstance(img_data, str):
                                logger.warning(f"无效的图片数据: {img_data}")
                                continue

                            filename = f"{nanoid.generate(size=10)}.png"
                            oss_url = None

                            # 检查数据类型并相应处理
                            if is_url(img_data):
                                # 如果是URL，使用save_image_by_url上传
                                logger.info(f"检测到URL类型图片，开始处理: {img_data[:30]}...")
                                try:
                                    oss_url = await save_image_by_url(img_data)
                                except Exception as e:
                                    logger.error(f"URL图片上传失败: {str(e)}")
                                    continue
                            elif is_base64_image(img_data):
                                # 如果是base64数据，使用save_image_b64data处理
                                logger.info(f"检测到Base64类型图片，开始处理...")
                                try:
                                    # 处理带有MIME前缀的base64数据
                                    base64_data = img_data
                                    if "," in img_data:
                                        _, base64_data = img_data.split(",", 1)

                                    # 处理base64 padding问题
                                    padding_needed = len(base64_data) % 4
                                    if padding_needed:
                                        base64_data += "=" * (4 - padding_needed)

                                    oss_url = save_image_b64data(base64_data, filename)
                                except Exception as e:
                                    logger.error(f"Base64图片处理失败: {str(e)}")
                                    continue
                            else:
                                logger.warning(f"未知格式的图片数据: {img_data[:30]}...")
                                continue

                            if oss_url:
                                oss_image_urls.append(oss_url)
                                logger.info(f"图片已上传到OSS: {oss_url}")
                            else:
                                logger.warning(f"图片上传到OSS失败")
                        except Exception as e:
                            logger.error(f"处理图片时发生错误: {str(e)}")

                logger.info(f"处理后的OSS图片URL列表: {oss_image_urls}")

                # 检查：如果用户提供了图片但全部上传失败，则直接返回失败
                if image_urls and not oss_image_urls:
                    # 更新任务状态为失败
                    task.status = ChatMjTasksStatus.FAILURE
                    task.fail_reason = "参考图片上传失败，请重试"
                    task.uptime = datetime.now()
                    task.progress = "100%"

                    await db.commit()

                    logger.error(f"图像生成失败：所有图片上传到OSS均失败，任务ID: {taskid}")
                    return

                # 处理prompt，添加比例参数
                final_prompt = prompt_en
                if not proportion and oss_image_urls:
                    # 如果没有指定比例且有输入图片，自动检测第一张图片的比例
                    try:
                        detected_proportion = await get_image_proportion(oss_image_urls[0])
                        final_prompt = f"{prompt_en} --ar {detected_proportion}"
                        logger.info(f"自动检测到图片比例: {detected_proportion}")
                    except Exception as e:
                        logger.warning(f"图片比例检测失败，使用原始prompt: {str(e)}")
                elif proportion:
                    # 如果指定了比例，直接添加到prompt
                    final_prompt = f"{prompt_en} --ar {proportion}"
                    logger.info(f"使用指定比例: {proportion}")

                # 创建协程列表，用于并发调用create_image_with_gpt函数
                coroutines = []

                # 为每个需要生成的图片创建一个协程
                pre_debit_log = []
                model = 'gpt-4o-image-vip'
                for _ in range(num):
                    coroutine = create_image_with_gpt(
                        prompt=final_prompt,
                        image_urls=oss_image_urls,  # 使用OSS URL替代原始的image_urls
                        model=model,
                        # n=num
                    )
                    coroutines.append(coroutine)
                    log_id = await operator.pre_debit(1, 'image-generation', model)
                    pre_debit_log.append(log_id)

                # 并发执行所有协程
                results = await asyncio.gather(*coroutines, return_exceptions=True)

                # 处理结果，过滤掉异常和None值
                images_url = []
                error_messages = []
                rollback_ids = []
                for idx, result in enumerate(results):
                    if result is not None and not isinstance(result, Exception):
                        images_url.append(result)
                    else:
                        if isinstance(result, Exception):
                            error_messages.append(str(result))
                        rollback_ids.append(pre_debit_log[idx])

                await operator.rollback(rollback_ids)
                logger.info(f"images_url: {images_url}")
                logger.info(f"生成了 {len(images_url)} 张图片")
                await operator.done()

                # 检查是否所有请求都失败了
                if not images_url and error_messages:
                    # 所有请求都失败，更新任务状态为失败
                    task.status = ChatMjTasksStatus.FAILURE
                    # 使用第一个异常信息作为失败原因
                    task.fail_reason = error_messages[0]
                    task.uptime = datetime.now()
                    task.progress = "100%"

                    await db.commit()

                    logger.error(f"图像生成失败：所有图片生成请求失败，任务ID: {taskid}，错误: {error_messages[0]}")
                    return

                # 将生成的图片上传到OSS
                oss_urls = []
                for img_url in images_url:
                    try:
                        # 使用service.file中的save_image_by_url函数上传图片到OSS
                        oss_url = await save_image_by_url(img_url)
                        if oss_url:
                            oss_urls.append(oss_url)
                    except Exception as e:
                        logger.error(f"上传图片到OSS失败: {str(e)}")

                logger.info(f"oss_urls: {oss_urls}")

                # 加锁，确保同一时间只有一个任务更新同一个数据库记录
                async with task_specific_lock:
                    # 从Redis获取当前image_urls，如果不存在则从数据库获取
                    current_urls = await get_image_urls_from_redis(task.taskid)
                    
                    if current_urls is None:
                        # Redis中没有数据，从数据库获取
                        result = await db.execute(select(ChatMjTasks).where(ChatMjTasks.id == task_id))
                        task = result.scalars().first()
                        
                        if not task:
                            logger.error(f"任务已不存在: {task_id}")
                            return
                        
                        # 解析数据库中的image_urls
                        if task.image_urls:
                            try:
                                current_urls = json.loads(task.image_urls)
                                if not isinstance(current_urls, list):
                                    current_urls = []
                            except Exception as e:
                                logger.error(f"解析任务的image_urls失败: {str(e)}")
                                current_urls = []
                        else:
                            current_urls = []
                    
                    # 将新生成的图片URL添加到列表中（新图片在前）
                    logger.info(f"新生成的oss_urls: {oss_urls}")
                    logger.info(f"当前从Redis/数据库获取的current_urls: {current_urls}")
                    merged_urls = oss_urls + current_urls
                    logger.info(f"合并后的merged_urls: {merged_urls}")
                    
                    # 更新任务状态
                    task.status = ChatMjTasksStatus.SUCCESS
                    task.finish_time = datetime.now()
                    
                    # 设置第一张图片为主图（如果之前没有设置过）
                    if not task.image_url and oss_urls:
                        task.image_url = oss_urls[0]
                    
                    # 保存合并后的图片URL到Redis
                    await save_image_urls_to_redis(task.taskid, merged_urls)
                    
                    # 同时更新数据库
                    task.image_urls = json.dumps(merged_urls)
                    task.uptime = datetime.now()
                    task.progress = "100%"
                    task.prompt_img = json.dumps(oss_image_urls)
                    
                    await db.commit()
                    await db.refresh(task)

                # 将资产信息存储到资产表
                try:
                    # 根据username查询用户实例
                    user_stmt = select(User).filter(User.username == task.username)
                    user_result = await db.execute(user_stmt)
                    task_user = user_result.scalars().first()

                    if task_user:
                        # 存储资产到资产表
                        asset = await store_asset_from_instance(task, task_user, db)
                        if asset:
                            logger.info(f"成功将GPT图像任务存储到资产表，资产ID: {asset.id}")
                        else:
                            logger.warning(f"GPT图像任务存储到资产表失败，任务ID: {task.id}")
                    else:
                        logger.error(f"未找到用户: {task.username}，无法存储资产")
                except Exception as e:
                    logger.error(f"存储GPT图像资产失败: {str(e)}")

                logger.info(f"图像生成成功，任务ID: {taskid}")

                # 任务完成后释放锁（虽然with语句会自动释放，但这里明确释放）
                if task_id in task_locks:
                    with task_lock:
                        if task_id in task_locks and not task_locks[task_id].locked():
                            del task_locks[task_id]
                            logger.info(f"已释放任务锁: {task_id}")

            except Exception as e:
                # 更新任务状态为失败
                async with task_specific_lock:
                    result = await db.execute(select(ChatMjTasks).where(ChatMjTasks.id == task_id))
                    task = result.scalars().first()
                    if task:
                        task.status = ChatMjTasksStatus.FAILURE
                        task.fail_reason = str(e)
                        task.uptime = datetime.now()
                        task.progress = "100%"
                        await db.commit()

                logger.error(f"图像生成失败: {str(e)}")

    except Exception as e:
        logger.error(f"后台任务处理失败: {str(e)}")


@router.post("/generate", tags=["gptimage"], response_model=GptImageResponse)
async def generate_image(
    request: Request,
    payload: GptImageRequest,
    background_tasks: BackgroundTasks,
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db),
):
    """
    使用GPT-4o-image模型生成图片

    Args:
        prompt: 提示词
        image_urls: 图片URL列表
        taskid: 任务ID，默认为空（可选）
        num: 生成图片数量，默认为1
        regenerate_index: 要重新生成的图片索引（可选）

    Returns:
        任务信息
    """
    if not payload.prompt:
        raise ClientVisibleException("提示词不能为空")

    # 验证 num 参数
    if payload.num > 4:
        raise ClientVisibleException("最大生成上限为4")

    prompt = payload.prompt
    prompt_en = prompt

    # 如果包含中文，进行翻译
    if contains_chinese(prompt):
        try:
            prompt_en = await get_translate(prompt, "zh-CN", "en")
        except Exception as e:
            logger.error(f"翻译失败: {str(e)}")
            raise ClientVisibleException("翻译失败") from e

    try:
        # 检查是否提供了taskid
        task = None
        if payload.taskid:
            # 查询是否存在该任务
            result = await db.execute(
                select(ChatMjTasks).where(
                    ChatMjTasks.taskid == payload.taskid,
                    ChatMjTasks.username == user.username
                )
            )
            task = result.scalars().first()

            # 如果找到任务且提供了regenerate_index参数，处理image_urls删除指定的空URL
            if task and payload.regenerate_index is not None:
                try:
                    # 1.首先从SQL数据库获取最新数据
                    existing_urls = []
                    if task.image_urls:
                        try:
                            existing_urls = json.loads(task.image_urls)
                            if not isinstance(existing_urls, list):
                                logger.warning(f"task.image_urls不是列表类型: {type(existing_urls)}")
                                existing_urls = []
                        except Exception as e:
                            logger.error(f"解析任务的image_urls失败: {str(e)}")
                    
                    # 2.将数据库中的数据设置到Redis
                    await save_image_urls_to_redis(task.taskid, existing_urls)
                    logger.info(f"从数据库获取并保存到Redis: {existing_urls}")
                    
                    # 3.从Redis中获取数据进行后续处理
                    existing_urls = await get_image_urls_from_redis(task.taskid)
                    if existing_urls is None:
                        existing_urls = []
                    
                    # 检查索引是否超出范围
                    if payload.regenerate_index is not None:
                        if payload.regenerate_index < 0:
                            logger.error(f"regenerate_index为负数: {payload.regenerate_index}")
                        elif len(existing_urls) == 0:
                            logger.error(f"existing_urls为空列表，但提供了regenerate_index: {payload.regenerate_index}")
                        elif payload.regenerate_index >= len(existing_urls):
                            logger.error(f"regenerate_index超出范围: {payload.regenerate_index}, 列表长度: {len(existing_urls)}")
                        # 只有当索引有效时才执行删除操作
                        elif 0 <= payload.regenerate_index < len(existing_urls):
                            # 检查该索引是否是空URL（即失败的图片）
                            if existing_urls[payload.regenerate_index] == "":
                                logger.info(f"移除索引 {payload.regenerate_index} 处的空URL用于重新生成")
                                logger.info(f"移除前 existing_urls: {existing_urls}")
                                # 删除指定索引的空URL
                                existing_urls.pop(payload.regenerate_index)
                                
                                # 更新Redis中的数据
                                await save_image_urls_to_redis(task.taskid, existing_urls)
                                
                                # 更新任务的image_urls
                                task.image_urls = json.dumps(existing_urls)
                                await db.commit()
                                await db.refresh(task)
                                
                                logger.info(f"移除后 existing_urls: {existing_urls}")
                            else:
                                logger.warning(f"索引 {payload.regenerate_index} 处的URL不为空，跳过删除")
                                logger.info(f"existing_urls: {existing_urls}")
                except Exception as e:
                    logger.error(f"处理regenerate_index删除空URL失败: {str(e)}")
                    logger.exception("删除空URL详细错误")

        # 如果任务不存在或未提供taskid，则创建新任务
        if not task:
            # 生成任务ID (时间戳)
            taskid = str(int(time.time() * 1000))

            # 创建任务记录，状态为提交
            task = ChatMjTasks(
                username=user.username,
                taskid=taskid,
                action=ChatMjTasksAction.IMAGINE,
                status=ChatMjTasksStatus.SUBMITTED,  # 设置状态为已提交
                prompt=prompt,
                prompt_en=prompt_en,
                submit_time=datetime.now(),
                start_time=datetime.now(),
                uptime=datetime.now(),
                model=ChatMjTasksModel.OPENAI,
                manufacturer=ChatMjTasksModel.OPENAI
            )

            # 保存到数据库
            db.add(task)
            await db.commit()
            await db.refresh(task)
            
            # 初始化Redis中的image_urls为空列表
            await save_image_urls_to_redis(task.taskid, [])
        else:
            # 更新现有任务
            task.status = ChatMjTasksStatus.SUBMITTED
            task.submit_time = datetime.now()
            task.start_time = datetime.now()
            task.uptime = datetime.now()
            task.fail_reason = ""

            # 如果不保留历史，则清空image_url和image_urls并更新Redis
            if not payload.preserve_history:
                task.image_url = None
                task.image_urls = None
                await save_image_urls_to_redis(task.taskid, [])

            await db.commit()
            await db.refresh(task)

        operator = CreditOperator(
            user_id=user.id,
            ip=request.state.client_ip,
            editor=user.username,
            db=db,
        )
        
        # 根据具体情况决定生成图片数量
        generation_count = payload.num
        logger.info(f"生成请求，生成{generation_count}张图")
        
        # 创建独立的生成任务
        for i in range(generation_count):
            logger.info(f"创建第 {i+1}/{generation_count} 个图片生成任务")
            # 添加后台任务
            background_tasks.add_task(
                process_image_generation,
                prompt_en=prompt_en,
                image_urls=payload.image_urls,
                task_id=task.id,
                num=1,  # 每个任务只生成一张图片
                taskid=task.taskid,
                preserve_history=payload.preserve_history,  # 使用用户指定的preserve_history参数
                proportion=payload.proportion,
                operator=operator,
            )

        # 返回结果
        return GptImageResponse(data=ChatMjTasksOut(**task.__dict__))

    except Exception as e:
        logger.error(f"创建任务失败: {str(e)}")
        raise ClientVisibleException("创建任务失败") from e


@router.get("/task/{taskid}", tags=["gptimage"], response_model=GptImageResponse)
async def get_task(
    taskid: str,
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db),
):
    """
    查询指定任务

    Args:
        taskid: 任务ID

    Returns:
        任务信息
    """
    try:
        # 查询任务
        result = await db.execute(
            select(ChatMjTasks).where(
                ChatMjTasks.taskid == taskid,
                ChatMjTasks.username == user.username
            )
        )
        task = result.scalars().first()

        if not task:
            raise ClientVisibleException("任务不存在")

        # 检查任务是否超时（如果状态为已提交且提交时间超过10分钟）
        if task.status == ChatMjTasksStatus.SUBMITTED:
            current_time = datetime.now()
            time_difference = current_time - task.submit_time

            # 如果超过10分钟（600秒）
            if time_difference.total_seconds() > 600:
                # 更新任务状态为失败
                task.status = ChatMjTasksStatus.FAILURE
                task.fail_reason = "任务处理超时"
                task.uptime = current_time

                # 提交更改
                await db.commit()
                await db.refresh(task)

                logger.warning(f"任务 {taskid} 已超时，状态已更新为失败")

        # 返回结果
        return GptImageResponse(data=ChatMjTasksOut(**task.__dict__))

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询任务失败: {str(e)}")
        raise ClientVisibleException("查询任务失败") from e

## pan prompt 映射字典
pan_prompt_mapping = {
    "btn1": "Extend the image to the left", # 向左扩展
    "btn2": "Extend the image to the right", # 向右扩展
    "btn3": "Extend the image upward", # 向上扩展
    "btn4": "Extend the image downward", # 向下扩展
}


@router.post("/change/pan", tags=["gptimage"], response_model=GptImageResponse)
async def pan_image(
    request: Request,
    payload: GptImageRequest,
    background_tasks: BackgroundTasks,
    user: User = Depends(get_request_user),
    db: AsyncSession = Depends(get_db),
):
    """
    使用GPT-4o-image模型扩展图片（向左、向右、向上、向下）

    Args:
        prompt: 按钮标识（btn1, btn2, btn3, btn4）
        image_urls: 图片URL列表（支持URL和base64数据）
        taskid: 任务ID，默认为空（可选）
        num: 生成图片数量，默认为1
        regenerate_index: 要重新生成的图片索引（可选）

    Returns:
        任务信息
    """
    if not payload.prompt:
        raise ClientVisibleException("提示词不能为空")

    # 验证 prompt 参数是否为有效的按钮标识
    if payload.prompt not in pan_prompt_mapping:
        raise ClientVisibleException("无效的按钮标识")

    # 验证 num 参数
    if payload.num > 4:
        raise ClientVisibleException("最大生成上限为4")

    # 从映射字典中获取对应的英文提示词
    prompt = payload.prompt
    prompt_en = pan_prompt_mapping[prompt]

    try:
        # 检查是否提供了taskid
        task = None

        if payload.taskid:
            # 查询是否存在该任务
            result = await db.execute(
                select(ChatMjTasks).where(
                    ChatMjTasks.taskid == payload.taskid,
                    ChatMjTasks.username == user.username
                )
            )
            task = result.scalars().first()
            
            # 如果找到任务且提供了regenerate_index参数，处理image_urls删除指定的空URL
            if task and payload.regenerate_index is not None:
                try:
                    # 1.首先从SQL数据库获取最新数据
                    existing_urls = []
                    if task.image_urls:
                        try:
                            existing_urls = json.loads(task.image_urls)
                            if not isinstance(existing_urls, list):
                                logger.warning(f"task.image_urls不是列表类型: {type(existing_urls)}")
                                existing_urls = []
                        except Exception as e:
                            logger.error(f"解析任务的image_urls失败: {str(e)}")
                    
                    # 2.将数据库中的数据设置到Redis
                    await save_image_urls_to_redis(task.taskid, existing_urls)
                    logger.info(f"从数据库获取并保存到Redis: {existing_urls}")
                    
                    # 3.从Redis中获取数据进行后续处理
                    existing_urls = await get_image_urls_from_redis(task.taskid)
                    if existing_urls is None:
                        existing_urls = []
                    
                    # 检查索引是否超出范围
                    if payload.regenerate_index is not None:
                        if payload.regenerate_index < 0:
                            logger.error(f"扩图 regenerate_index为负数: {payload.regenerate_index}")
                        elif len(existing_urls) == 0:
                            logger.error(f"扩图 existing_urls为空列表，但提供了regenerate_index: {payload.regenerate_index}")
                        elif payload.regenerate_index >= len(existing_urls):
                            logger.error(f"扩图 regenerate_index超出范围: {payload.regenerate_index}, 列表长度: {len(existing_urls)}")
                        # 只有当索引有效时才执行删除操作
                        elif 0 <= payload.regenerate_index < len(existing_urls):
                            # 检查该索引是否是空URL（即失败的图片）
                            if existing_urls[payload.regenerate_index] == "":
                                logger.info(f"移除索引 {payload.regenerate_index} 处的空URL用于扩图重新生成")
                                logger.info(f"移除前 existing_urls: {existing_urls}")
                                # 删除指定索引的空URL
                                existing_urls.pop(payload.regenerate_index)
                                
                                # 更新Redis中的数据
                                await save_image_urls_to_redis(task.taskid, existing_urls)
                                
                                # 更新任务的image_urls
                                task.image_urls = json.dumps(existing_urls)
                                await db.commit()
                                await db.refresh(task)
                                
                                logger.info(f"移除后 existing_urls: {existing_urls}")
                            else:
                                logger.warning(f"索引 {payload.regenerate_index} 处的URL不为空，跳过删除")
                except Exception as e:
                    logger.error(f"处理扩图 regenerate_index删除空URL失败: {str(e)}")
                    logger.exception("扩图删除空URL详细错误")

        taskid = str(int(time.time() * 1000))

        # 创建任务记录，状态为提交
        task = ChatMjTasks(
            username=user.username,
            taskid=taskid,
            action=ChatMjTasksAction.PAN,  # 设置action为PAN
            status=ChatMjTasksStatus.SUBMITTED,
            prompt=prompt_en,
            prompt_en=prompt_en,
            submit_time=datetime.now(),
            start_time=datetime.now(),
            uptime=datetime.now(),
            model=ChatMjTasksModel.OPENAI,
            manufacturer=ChatMjTasksModel.OPENAI
        )

        # 保存到数据库
        db.add(task)
        await db.commit()
        await db.refresh(task)

        # 更新现有任务
        task.status = ChatMjTasksStatus.SUBMITTED
        task.action = ChatMjTasksAction.PAN  # 更新action为PAN
        task.prompt = prompt_en
        task.prompt_en = prompt_en
        task.submit_time = datetime.now()
        task.start_time = datetime.now()
        task.uptime = datetime.now()

        # 如果不保留历史，则清空image_url和image_urls
        if not payload.preserve_history:
            task.image_url = None
            task.image_urls = None

        await db.commit()
        await db.refresh(task)

        operator = CreditOperator(
            user_id=user.id,
            ip=request.state.client_ip,
            editor=user.username,
            db=db,
        )
        # 根据用户指定的num参数创建对应数量的任务
        generation_count = payload.num
        logger.info(f"扩图请求，生成{generation_count}张图")
        
        # 创建独立的生成任务
        for i in range(generation_count):
            logger.info(f"创建第 {i+1}/{generation_count} 个扩图任务")
            # 添加后台任务
            background_tasks.add_task(
                process_image_generation,
                prompt_en=prompt_en,
                image_urls=payload.image_urls,
                task_id=task.id,
                num=1,  # 每个任务只生成一张图片
                taskid=task.taskid,
                preserve_history=payload.preserve_history,
                proportion=payload.proportion,
                operator=operator,
            )

        # 返回结果
        return GptImageResponse(data=ChatMjTasksOut(**task.__dict__))

    except Exception as e:
        logger.error(f"创建PAN任务失败: {str(e)}")
        raise ClientVisibleException("创建PAN任务失败") from e


@router.get("/check")
async def check_quota():
    """
    获取OpenAI API Key的余额信息
    """
    url = "https://api.pumpkinaigc.online/public/dashboard/billing/subscription"
    headers = {
        "Authorization": f"Bearer {app_settings.openai_image_api_key}"
    }

    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()  # 检查响应状态

        data = response.json()

        # 计算余额
        remain_quota = data.get("remain_quota", 0)
        balance_usd = remain_quota * 0.000002

        return {
            "code": "0000",
            "data": {
                "quota": {
                    "balance_usd": round(balance_usd, 2)
                },
                "res": data  # 完整响应
            }
        }

    except requests.exceptions.RequestException as e:
        raise ClientVisibleException("API请求失败") from e
    except ValueError as e:
        raise ClientVisibleException("解析响应数据失败") from e
    except Exception as e:
        raise ClientVisibleException("处理请求时发生错误") from e
