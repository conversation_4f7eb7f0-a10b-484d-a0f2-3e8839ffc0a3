import asyncio
import uuid
from typing import Any

from fastapi import APIRouter, BackgroundTasks, Depends
import os
from pydantic import BaseModel
from base64 import b64encode
import httpx
from sqlalchemy.ext.asyncio import AsyncSession

from config import app_settings
from models.assets import AssetType
from models.users import get_request_user, User
from service.file import save_audio
from utils.asset_storage import store_asset_directly
from utils.database import get_db
from utils.exceptions import ClientVisibleException

router = APIRouter()


class ClearVoiceRequest(BaseModel):
    file_path: str
    model: str


async def save_processed_audio(
        db: AsyncSession,
        url: str | list[str],
        biz_id: str,
        user_id: int,
        parameter: dict[str, Any] = None,
):
    if isinstance(url, list):
        tasks = []
        for u in url:
            tasks.append(asyncio.to_thread(save_audio, u, f'{biz_id}_{user_id}_{uuid.uuid4()}.mp3'))
        url = await asyncio.gather(*tasks)
    else:
        url = await asyncio.to_thread(save_audio, url, f'{biz_id}_{user_id}_{uuid.uuid4()}.mp3')
    parameter = parameter.copy() if parameter else {}
    audio_base64 = parameter.pop("audio_base64", "")
    audio_url = ""
    if audio_base64:
        audio_url = await asyncio.to_thread(save_audio, audio_base64, f'{biz_id}_{user_id}_{uuid.uuid4()}.mp3')
    parameter["audio_url"] = audio_url
    await store_asset_directly(
        db=db,
        asset_type=AssetType.AUDIO,
        url=url,
        user_id=user_id,
        biz_id=biz_id,
        parameter=parameter,
    )

async def process_audio(req: ClearVoiceRequest, endpoint: str, user: User, db: AsyncSession, background_tasks: BackgroundTasks):
    """
    通用的音频处理函数，根据不同的 endpoint 调用不同的服务。
    :param req: 请求体，包括 file_path 和 model
    :param endpoint: 指定调用 clearvoice 服务的路径，如 "enhancement" 或 "separation"
    :return: 处理后的结果或错误信息
    """
    # 1. 判断文件是否存在
    if not os.path.exists(req.file_path):
        raise ClientVisibleException("音频文件不存在")

    # 2. 读取文件并转成base64
    with open(req.file_path, "rb") as audio_file:
        audio_base64 = b64encode(audio_file.read()).decode('utf-8')

    # 3. 组装请求体
    data = {
        "audio_base64": audio_base64,
        "model": req.model
    }

    # 4. 调用后端服务
    clearvoice_service_url = f"{app_settings.ai_server}/clearvoice/{endpoint}"
    async with httpx.AsyncClient(timeout=300) as client:
        try:
            response = await client.post(
                url=clearvoice_service_url,
                headers={"Content-Type": "application/json"},
                json=data
            )
            response.raise_for_status()
            response_data = response.json()
            response_data["type"] = 'data:audio/wav;'
            if endpoint == "enhancement":
                url = response_data.get('enhanced_audio', '')
            elif endpoint == "separation":
                url = [response_data.get('speaker_1', ''), response_data.get('speaker_2', '')]
            background_tasks.add_task(
                save_processed_audio,
                db=db,
                url=url,
                user_id=user.id,
                biz_id=f'audio_{endpoint}_speaker',
                parameter=data,
            )
            return {"code": "0000", "data": response_data}
        except httpx.TimeoutException as e:
            raise ClientVisibleException("请求超时") from e
        except httpx.HTTPError as e:
            raise ClientVisibleException("请求失败，请重试") from e


@router.post("/enhancement")
async def enhancement(
        req: ClearVoiceRequest,
        background_tasks: BackgroundTasks,
        user: User = Depends(get_request_user),
        db: AsyncSession = Depends(get_db),
):
    """
    语音增强接口
    """
    res = await process_audio(req, "enhancement", user, db, background_tasks)
    return res


@router.post("/separation")
async def separation(
        req: ClearVoiceRequest,
        background_tasks: BackgroundTasks,
        user: User = Depends(get_request_user),
        db: AsyncSession = Depends(get_db),
):
    """
    语音分离接口
    """
    return await process_audio(req, "separation", user, db, background_tasks)
