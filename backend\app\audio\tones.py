import asyncio
import logging
from datetime import datetime, timedelta
from enum import StrEnum
from typing import Annotated, Literal

from fastapi import APIRouter, Depends, Query, Path
from pydantic import BaseModel, Field, HttpUrl
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession

from config import app_settings
from models.audio_tones import AudioTone
from models.users import User, get_request_user
from service.tencent.cos import client
from utils.database import get_db
from utils.exceptions import ClientVisibleException
from utils.response import Pagination, SuccessRes

router = APIRouter()
logger = logging.getLogger(__name__)

NOISE_REDUCTION_TIMEOUT = timedelta(minutes=2)
"""音频降噪任务的等待时间"""
NOISE_REDUCTION_INTERVAL = timedelta(seconds=3)
"""音频降噪任务的轮询间隔"""


class Lang(StrEnum):
    """
    Key 来自于 ISO 639-3 标准。
    https://iso639-3.sil.org/code_tables/639/data/all

    业务中使用的是 Value 值。
    """
    CMN = '中文-普通话'
    YUE = '中文-粤语'
    ENG = '英语'
    JPN = '日语'
    KOR = '韩语'


class SaveTone(BaseModel):
    id: int = Field(description='音色 ID')
    name: str = Field(description='音色名称')
    gender: Literal[0, 1] | None = Field(description='性别')
    lang: Lang | None = Field(description='语言')
    description: str = Field(description='描述')
    create_time: datetime = Field(description='创建时间')
    update_time: datetime = Field(description='更新时间')
    is_favorite: bool = Field(description='是否收藏')
    audio_url: HttpUrl = Field(description='音频链接')
    is_reduction: bool = Field(description='是否已经降噪处理')


@router.get("/", response_model=SuccessRes[Pagination[SaveTone]])
async def list_tones(
        user: Annotated[User, Depends(get_request_user)],
        db: Annotated[AsyncSession, Depends(get_db)],
        size: Annotated[int, Query(ge=1, description='分页大小')] = 10,
        current: Annotated[int, Query(ge=1, description='页码')] = 1,
        gender: Annotated[int | None, Query(description='性别')] = None,
        lang: Annotated[Lang | None, Query(description='语言')] = None,
        is_favorite: Annotated[bool | None, Query(description='是否收藏')] = None,
):
    skip = (current - 1) * size
    records_stmt = (select(AudioTone)
                    .where(AudioTone.user_id == user.id)
                    .offset(skip).limit(size)
                    .order_by(AudioTone.create_time.desc()))
    total_stmt = select(func.count()).select_from(AudioTone).where(AudioTone.user_id == user.id)
    if gender is not None:
        records_stmt = records_stmt.where(AudioTone.gender == gender)
        total_stmt = total_stmt.where(AudioTone.gender == gender)
    if lang is not None:
        records_stmt = records_stmt.where(AudioTone.lang == lang)
        total_stmt = total_stmt.where(AudioTone.lang == lang)
    if is_favorite is not None:
        records_stmt = records_stmt.where(AudioTone.is_favorite == is_favorite)
        total_stmt = total_stmt.where(AudioTone.is_favorite == is_favorite)
    async with db as session:
        records_result = await session.execute(records_stmt)
        items = records_result.scalars().all()
        total_result = await session.execute(total_stmt)
        total = total_result.scalar()
    records = []
    for item in items:
        records.append(SaveTone.model_validate(item, from_attributes=True))
    r = Pagination(
        records=records,
        size=size,
        current=current,
        total=total,
    )
    return SuccessRes(data=r)


@router.get("/{tone_id}", response_model=SuccessRes[SaveTone])
async def get_tone(
        user: Annotated[User, Depends(get_request_user)],
        db: Annotated[AsyncSession, Depends(get_db)],
        tone_id: Annotated[int, Path(description='音色 ID')],
):
    stmt = select(AudioTone).where(AudioTone.user_id == user.id).where(AudioTone.id == tone_id)
    async with db as session:
        result = await session.execute(stmt)
        item = result.scalar()
    if item is None:
        raise ClientVisibleException('音色不存在')
    return SuccessRes(data=SaveTone.model_validate(item, from_attributes=True))


class ModifyToneReq(BaseModel):
    name: str = Field(min_length=1, max_length=20, description='音色名称')
    gender: Literal[0, 1] | None = Field(default=None, description='性别')
    lang: Lang | None = Field(default=None, min_length=1, max_length=32, description='语言')
    description: str = Field(default='', description='描述')
    audio_url: HttpUrl = Field(description='音频链接')
    is_favorite: bool = Field(default=False, description="是否收藏")
    need_reduction: bool = Field(default=False, description='是否需要降噪')

async def do_reduction_job(audio_url: HttpUrl) -> str:
    path = audio_url.path[1:]
    arr = path.rsplit('.', 1)
    if len(arr) >= 1:
        opt_path = arr[0] + '_Reduction'
    else:
        opt_path = path + '_Reduction'
    if len(arr) >= 2:
        opt_path += '.' + arr[1]
    logger.info(f"Start reduction for tone: {path}")
    job_body = {
        'Input': {
            'Object': path
        },
        'Tag': 'NoiseReduction',
        'Operation': {
            'Output': {
                'Region': app_settings.tencent_cos_region,
                'Bucket': app_settings.tencent_cos_bucket,
                'Object': opt_path
            }
        }
    }
    res: dict = await asyncio.to_thread(
        client.ci_create_media_jobs,
        Bucket=app_settings.tencent_cos_bucket,
        Jobs=job_body,
        Lst={},
        ContentType='application/xml'
    )
    job_id = res.get('JobsDetail', [dict()])[0].get('JobId')
    if job_id is None:
        logger.error(f"音频降噪任务 ID 返回为空, res: {res}")
        raise ClientVisibleException('降噪失败')
    # 开始轮询
    st = datetime.now()
    while datetime.now() - st < NOISE_REDUCTION_TIMEOUT:
        res = await asyncio.to_thread(
            client.ci_get_media_jobs,
            Bucket=app_settings.tencent_cos_bucket,
            JobIDs=job_id,
            ContentType='application/xml'
        )
        job_details = res.get('JobsDetail', [])
        if not job_details:
            logger.error(f"找不到音频降噪任务, job_id: {job_id}")
            raise ClientVisibleException("降噪失败")
        job_detail = job_details[0]
        state = job_detail.get('State', None)
        if state != 'Success':
            # 未完成，继续等待
            await asyncio.sleep(NOISE_REDUCTION_INTERVAL.total_seconds())
            continue
        output_key = job_detail.get('Operation', {}).get('Output', {}).get('Object')
        # 成功之后还要给新的对象加上 Content-Disposition
        # https://cloud.tencent.com/document/product/436/65822
        fn = output_key.split("/")[-1]
        await asyncio.to_thread(
            client.copy_object,
            Bucket=app_settings.tencent_cos_bucket,
            Key=output_key,
            CopySource={
                'Bucket': app_settings.tencent_cos_bucket,
                'Key': output_key,
                'Region': app_settings.tencent_cos_region,
            },
            CopyStatus='Replaced',  # 替换，而非新建一个对象
            Metadata={'Content-Disposition': f'attachment; filename="{fn}"'}
        )
        return f"{app_settings.tencent_cos_host}{output_key}"
    logger.error(f"降噪任务等待超时，job_id: {job_id}")
    raise ClientVisibleException("降噪失败")


@router.post("/", response_model=SuccessRes[SaveTone])
async def create_tone(
        payload: ModifyToneReq,
        user: Annotated[User, Depends(get_request_user)],
        db: Annotated[AsyncSession, Depends(get_db)],
):
    if payload.need_reduction:
        audio_url = await do_reduction_job(payload.audio_url)
        is_reduction = True
    else:
        audio_url = str(payload.audio_url)
        is_reduction = False
    tone = AudioTone(
        user_id=user.id,
        name=payload.name,
        gender=payload.gender,
        lang=payload.lang,
        description=payload.description,
        audio_url=audio_url,
        create_time=datetime.now(),
        update_time=datetime.now(),
        is_favorite=payload.is_favorite,
        is_reduction=is_reduction,
    )
    async with db as session:
        session.add(tone)
        await session.commit()
        await session.refresh(tone)
    return SuccessRes(data=SaveTone.model_validate(tone, from_attributes=True))


@router.put("/{tone_id}", response_model=SuccessRes[SaveTone])
async def update_tone(
        payload: ModifyToneReq,
        tone_id: Annotated[int, Path(description='音色 ID')],
        user: Annotated[User, Depends(get_request_user)],
        db: Annotated[AsyncSession, Depends(get_db)],
):
    stmt = select(AudioTone).where(AudioTone.user_id == user.id).where(AudioTone.id == tone_id)
    async with db as session:
        result = await session.execute(stmt)
        tone = result.scalar()
        if tone is None:
            raise ClientVisibleException("音色不存在")
        if payload.need_reduction and not tone.is_reduction:
            audio_url = await do_reduction_job(payload.audio_url)
            tone.is_reduction = True
        else:
            audio_url = str(payload.audio_url)
        tone.name = payload.name
        tone.gender = payload.gender
        tone.lang = payload.lang
        tone.description = payload.description
        tone.audio_url = audio_url
        tone.is_favorite = payload.is_favorite
        tone.update_time = datetime.now()
        session.add(tone)
        await session.commit()
        await session.refresh(tone)
    return SuccessRes(data=SaveTone.model_validate(tone, from_attributes=True))


@router.delete("/{tone_id}", response_model=SuccessRes[SaveTone])
async def delete_tone(
        tone_id: Annotated[int, Path(description='音色 ID')],
        user: Annotated[User, Depends(get_request_user)],
        db: Annotated[AsyncSession, Depends(get_db)],
):
    stmt = select(AudioTone).where(AudioTone.user_id == user.id).where(AudioTone.id == tone_id)
    async with db as session:
        result = await session.execute(stmt)
        tone = result.scalar()
        if tone is None:
            raise ClientVisibleException("音色不存在")
        await session.delete(tone)
        await session.commit()
    return SuccessRes(data=SaveTone.model_validate(tone, from_attributes=True))
