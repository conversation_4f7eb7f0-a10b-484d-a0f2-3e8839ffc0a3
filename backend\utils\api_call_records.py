
from fastapi import Depends,  Request,Response
from sqlalchemy.ext.asyncio import AsyncSession
from models.api_call_records import APICallRecord
from utils.database import get_db
import pydash
from config import endpoint_config

# log_api_call(request, response)
async def log_api_call(request: Request,response: Response,duration:float=0,db: AsyncSession = Depends(get_db)):
  method     = request.method
  userid     = pydash.get(request.state, "user.id", 0)
  username   = pydash.get(request.state, "user.username", "")
  company    = pydash.get(request.state, "user.company", "")
  # route_name = pydash.get(request.scope, "route.name", "unknown")
  path       = pydash.get(request.scope, "route.path", "")
  if path not in endpoint_config["api_map"]:
    return False

  newRecord=APICallRecord(
    api_name             = endpoint_config["api_map"][path],
    api_path             = path,
    request_method       = method,
    response_status_code = response.status_code,
    user_id              = userid,
    username             = username,
    company              = company,
    duration             = duration,
  )
  db.add(newRecord)
  await db.commit()
  return True
