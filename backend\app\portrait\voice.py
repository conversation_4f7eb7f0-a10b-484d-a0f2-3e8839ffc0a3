import asyncio
import logging
import os
import time
import json
from fastapi.responses import FileResponse
from fastapi import APIRouter, Depends, BackgroundTasks
from pydantic import BaseModel
from pathlib import Path

from sqlalchemy.ext.asyncio import AsyncSession

from config import app_settings
from models.assets import AssetType
from service.file import save_audio
from utils.asset_storage import store_asset_directly
from utils.database import get_db
from utils.exceptions import ClientVisibleException
from utils.redis import redis
from urllib.parse import unquote
import httpx
from base64 import b64decode, b64encode
from typing import Optional, Any
import uuid
from models.users import User, get_request_user

router = APIRouter()
logger = logging.getLogger(__name__)


AI_SERVER = app_settings.ai_server
# role_models_path = "D:/AIGC/GPTSoVITS/语音模型"    # v1的音频路径
role_models_path = "G:/GPT-SoVITS模型"


class SeparateRequest(BaseModel):
    file_path: str
    model: str


class TTSExampleRequest(BaseModel):
    role: str = ''
    text: str
    prompt_text: str
    prompt_audio: str
    language: str
    file_path: Optional[str] = ''


class ChatTTSRequest(BaseModel):
    text: str
    voice: str = ''
    custom_voice: str = ''
    temperature: float = 0,
    top_k: float = 0,
    top_p: float = 0,
    text_seed: int = 0
    audio_seed: int = 0
    prompt_oral_status: bool
    prompt_break_status: bool
    prompt_laugh_status: bool
    prompt_oral: int
    prompt_break: int
    prompt_laugh: int


class SoVITSZeroShot(BaseModel):
    prompt_text: str
    prompt_language: str
    text: str
    text_language: str
    temperature: float
    speed: float
    top_k: int
    top_p: float
    prompt_audio_b64: Optional[str] = None
    reference_id: Optional[str] = None
    save_model: Optional[bool] = False


def delete_file_after_delay(filename: str):
    time.sleep(5)  # 延迟5秒
    if os.path.exists(filename):
        os.remove(filename)
        logger.info(f"{filename} has been deleted.")
    else:
        logger.info(f"File {filename} does not exist.")


# @router.post("/voice_separate_proxy", tags=["media"])
# async def voice_separate_proxy(request: Request):
#   target_url = f"{CUDA_OM}/xxx"
#   async with httpx.AsyncClient(timeout=60) as client:
#     # 构造新的请求，包括方法、查询参数和请求体等
#     response = await client.request(
#       method=request.method,
#       url=target_url,
#       headers=request.headers,
#       params=request.query_params,
#       content=await request.body()
#     )
#   return response

def get_text_language(text):
    for char in text:
        if '\u4e00' <= char <= '\u9fff':
            return '中文'
        elif ('\u0041' <= char <= '\u005a') or ('\u0061' <= char <= '\u007a'):
            return '英文'
        elif ('\u0800' <= char <= '\u4e00') or ('\u3040' <= char <= '\u30ff'):
            return '日文'


async def save_voice_separate_audio(
        db: AsyncSession,
        url: str | list[str],
        biz_id: str,
        user_id: int,
        parameter: dict[str, Any] = None,
):
    parameter = parameter.copy() if parameter else {}
    audio_base64 = parameter.pop("audio_base64", "")
    audio_url = ""
    if audio_base64:
        audio_url = await asyncio.to_thread(save_audio, audio_base64, f'{biz_id}_{user_id}_{uuid.uuid4()}.mp3')
    parameter["audio_url"] = audio_url
    await store_asset_directly(
        db=db,
        asset_type=AssetType.AUDIO,
        url=url,
        user_id=user_id,
        biz_id=biz_id,
        parameter=parameter,
    )


@router.post("/voice_separate", tags=["media"])
async def voice_separate(
        req: SeparateRequest,
        background_tasks: BackgroundTasks,
        db: AsyncSession = Depends(get_db),
        user: User = Depends(get_request_user),
):
    """
    声音分离，分离人声和背景音乐
    """
    if not os.path.exists(req.file_path):
        raise ClientVisibleException("音频文件不存在")

    async with httpx.AsyncClient(timeout=300) as client:
        with open(req.file_path, "rb") as audio_file:
            audio_base64 = b64encode(audio_file.read()).decode('utf-8')

        data = {
            "audio_base64": audio_base64,
            "model": req.model
        }

        try:
            response = await client.post(
                url=f'{AI_SERVER}/voiceSeparate/get_voice',  #
                headers={
                    "Content-Type": "application/json"
                },
                json=data
            )
            response.raise_for_status()
            response_data = response.json()
            response_data["type"] = 'data:audio/wav;'
            background_tasks.add_task(
                save_voice_separate_audio,
                db=db,
                url=[response_data.get('instrumental', ''), response_data.get('vocals', '')],
                user_id=user.id,
                biz_id='audio_separation_voice',
                parameter=data,
            )
            return {"code": "0000", "data": response_data}
        except httpx.TimeoutException as e:
            raise ClientVisibleException("请求超时，请重试") from e
        except Exception as e:
            logger.error(f"An error occurred: {e}")
            raise ClientVisibleException("系统服务调用失败") from e


@router.post("/tts", tags=["media"])
async def tts(
        req: TTSExampleRequest,
        background_tasks: BackgroundTasks,
        db: AsyncSession = Depends(get_db),
        user: User = Depends(get_request_user),
):
    """
    TEXT TO SPEECK
    """
    prompt_language = get_text_language(req.prompt_text)

    # 读取 JSON 文件内容
    with open('data/voice/voice_models.json', 'r', encoding='utf-8') as file:
        data = json.load(file)

    if req.prompt_audio == "custom":
        # 处理自定义音频上传的情况
        with open(req.file_path, "rb") as file:
            file_data = b64encode(file.read()).decode('utf-8')

        file_name = str(uuid.uuid4()) + '.wav'
        data = {
            "file_data": file_data,
            "file_name": file_name
        }
        url = f"{AI_SERVER}/upload/addfile"  #

        async with httpx.AsyncClient(timeout=300) as client:
            response = await client.post(url=url, json=data)
            response.raise_for_status()
            refer_wav_path = response.json().get("file")
    else:
        # 直接使用前端传入的路径，只需要加上基础路径
        base_path = "/data/models/gpt-sovits-v2"
        refer_wav_path = f"{base_path}/{req.prompt_audio}"

    # 确保路径使用正斜杠
    refer_wav_path = refer_wav_path.replace('\\', '/')
    logger.debug(f"Final refer_wav_path: {refer_wav_path}")

    async with httpx.AsyncClient(timeout=300) as client:
        try:
            params = {
                "refer_wav_path": refer_wav_path,
                "prompt_text": req.prompt_text,
                "prompt_language": prompt_language,
                "text": req.text,
                "text_language": req.language,
            }
            response = await client.get(
                url=f'{AI_SERVER}/gpt_sovits/tts',
                params=params
            )

            response.raise_for_status()
            logger.info(f"Response: {response.content}")

            # 解析JSON响应，获取URL
            response_data = response.json()
            if 'b64' in response_data and 'type' in response_data and response_data.get('type') == 'oss_url':
                url = response_data.get('b64')
                background_tasks.add_task(
                    store_asset_directly,
                    db=db,
                    asset_type=AssetType.AUDIO,
                    url=url,
                    user_id=user.id,
                    biz_id='tts_sovits',
                    parameter=req.model_dump(exclude_none=True),
                )
                # 直接返回URL
                return {"code": "0000", "data": {"b64": url}}
            else:
                # 兼容老逻辑，如果返回非URL则继续按原方式处理
                base64_encoded = b'data:audio/wav;base64,'
                async for chunk in response.aiter_bytes():
                    base64_encoded += b64encode(chunk)
                return {"code": "0000", "data": {"b64": base64_encoded}}
        except httpx.TimeoutException as e:
            raise ClientVisibleException("请求超时，请重试") from e
        except Exception as e:
            logger.error(f"An error occurred: {e}")
            raise ClientVisibleException("生成失败，请重试") from e
        finally:
            # 删除文件
            if req.prompt_audio == "custom":
                data = {
                    "file": refer_wav_path
                }
                url = f"{AI_SERVER}/upload/delfile"  #
                response = httpx.post(url, json=data)


import re


@router.post("/sovits_zero_shot", tags=["media"])
async def sovits_zero_shot(
        req: SoVITSZeroShot,
        background_tasks: BackgroundTasks,
        db: AsyncSession = Depends(get_db),
        user: User = Depends(get_request_user),
):
    if not req.prompt_audio_b64 and not req.reference_id:
        raise ClientVisibleException("请提供音频文件或参考ID")

    tmp_file_name = f'{uuid.uuid4()}.wav'

    prompt_text = req.prompt_text if req.save_model or not req.reference_id else req.reference_id
    if req.save_model and len(prompt_text) > 50:
        prompt_text = prompt_text[:50]  # 截取前50个字符

    formatted_text = re.sub(r'[^\w\s]', '', req.text)

    data = {
        "text": formatted_text,
        "text_language": req.text_language,
        "prompt_text": prompt_text,
        "prompt_language": req.prompt_language,
        "temperature": req.temperature,
        "speed": req.speed,
        "top_p": req.top_p,
        "top_k": req.top_k,
        "user": user.username,
        "reference_id": req.reference_id or "",
        "save_model": req.save_model,
        "file_path": '',
    }

    # print(f"data: {data}")
    # 初心5003
    upload_url = f"{AI_SERVER}/gpt_sovits/upload_file"
    zero_shot_url = f"{AI_SERVER}/gpt_sovits/gpt_sovits_zeroshort"  #

    async with httpx.AsyncClient(timeout=300) as client:
        try:
            if req.prompt_audio_b64:
                base64_str = req.prompt_audio_b64.split(',', 1)[1]
                file_data = b64decode(base64_str)
                files = {'file': (tmp_file_name, file_data)}
                response = await client.post(upload_url, files=files)
                response.raise_for_status()
                logger.debug(response.json())
                data["file_path"] = response.json().get("file_path", "")

            logger.debug(json.dumps(data, ensure_ascii=False, indent=4))
            response = await client.post(url=zero_shot_url, json=data)
            response.raise_for_status()

            # 解析JSON响应，获取URL
            try:
                response_data = response.json()
                if 'b64' in response_data and 'type' in response_data and response_data.get('type') == 'oss_url':
                    url = response_data.get('b64')
                    background_tasks.add_task(
                        store_asset_directly,
                        db=db,
                        asset_type=AssetType.AUDIO,
                        url=url,
                        user_id=user.id,
                        biz_id='audio_mimic_sovits',
                        parameter=data,
                    )
                    # 直接返回URL
                    return {"code": "0000", "data": {"b64": url}}
            except:
                pass

            # 兼容老逻辑，如果返回非URL则继续按原方式处理
            base64_encoded = b'data:audio/wav;base64,'
            async for chunk in response.aiter_bytes():
                base64_encoded += b64encode(chunk)

            return {"code": "0000", "data": {"b64": base64_encoded}}
        except httpx.HTTPError as error:
            logger.error(f'An error occurred: {error}')
            raise ClientVisibleException("Request error, please check the parameters.") from error


@router.post("/chat_tts", tags=["media"])
async def chat_tts(
        req: ChatTTSRequest,
        background_tasks: BackgroundTasks,
        db: AsyncSession = Depends(get_db),
        user: User = Depends(get_request_user),
):
    logger.info(f"CHAT_TTS:{AI_SERVER}/chat_tts/")
    # 提取数字部分
    req.voice = ''.join(filter(str.isdigit, req.voice))
    req.custom_voice = ''.join(filter(str.isdigit, req.custom_voice))

    prompt = ''
    if req.prompt_oral_status:
        prompt += f'[oral_{req.prompt_oral}]'
    if req.prompt_break_status:
        prompt += f'[break_{req.prompt_break}]'
    if req.prompt_laugh_status:
        prompt += f'[laugh_{req.prompt_laugh}]'

    data = {
        "text": req.text,
        "prompt": prompt,
        "voice": req.voice,
        "temperature": req.temperature,
        "top_p": req.top_p,
        "top_k": req.top_k,
        "refine_max_new_token": "384",
        "infer_max_new_token": "2048",
        "skip_refine": 0,
        "is_split": 1,
        "text_seed": req.text_seed,
        "audio_seed": req.audio_seed,
        "custom_voice": req.custom_voice
    }
    logger.info(f"data:{data}")
    async with httpx.AsyncClient(timeout=300) as client:
        try:
            # 获取TTS生成结果
            response = await client.post(
                url=f'{AI_SERVER}/chat_tts/',
                data=data
            )
            response.raise_for_status()

            # 获取响应数据
            response_data = response.json()
            logger.info(f"Response: {response_data}")

            audio_url = response_data["data"]["url"]
            logger.info("Found direct base64 audio URL in response")

            background_tasks.add_task(
                store_asset_directly,
                db=db,
                asset_type=AssetType.AUDIO,
                url=audio_url,
                user_id=user.id,
                biz_id='tts_chattts',
                parameter=data,
            )
            return {
                "code": "0000",
                "data": {
                    "url": audio_url
                }
            }

        except httpx.TimeoutException as e:
            raise ClientVisibleException("请求超时，请重试") from e
        except Exception as e:
            logger.error(f"chat tts An error occurred: {e}")
            raise ClientVisibleException("生成失败，请重试") from e


@router.get("/get_role_voice_example", tags=["media"])
async def get_role_voice_example():
    """
    读取声音样例
    """
    data = await redis.get('get_role_voice_example')
    if data is not None:
        return {"code": "0000", "data": json.loads(data)}

    logger.debug('无缓存，遍历文件')
    directory = 'data/voice/model'
    real_path = os.path.join('./', directory)
    logger.debug(directory)
    roles = []
    voice_example = {}
    with os.scandir(real_path) as entries:
        for entry in entries:
            if entry.is_dir():
                role_map = []
                roles.append(entry.name)

                # print(f"子目录: {entry.name}")

                role_path = os.path.join(directory, entry.name, '参考音频')

                # real_role_path = os.path.join('./',role_path)

                # print(f"遍历: {real_role_path}")
                with os.scandir(role_path) as role_entries:
                    exam_item: dict = {}
                    for role_entrie in role_entries:
                        if role_entrie.is_file():

                            file_path = os.path.join(role_path, role_entrie.name)
                            # print(f"文件: {file_path}")
                            file_name, file_extension = os.path.splitext(role_entrie.name)

                            if file_name not in exam_item:
                                exam_item[file_name] = {
                                    "name": file_name,
                                    "file_name": "",
                                    "content": "",
                                    "file_path": ""
                                }

                            if file_extension == '.txt':
                                with open(os.path.join('./', file_path), 'r', encoding='utf-8') as file:
                                    # 读取文件的全部内容
                                    exam_item[file_name]["content"] = file.read()
                            else:
                                p = Path(os.path.join(role_path, role_entrie.name))
                                exam_item[file_name]["file_name"] = role_entrie.name
                                exam_item[file_name]["file_path"] = p.as_posix()
                    # print(exam_item)
                    # role_map['expample']=list(exam_item.values())
                    role_map = list(exam_item.values())
                    # role_map=exam_item

                voice_example[entry.name] = role_map

    data = {
        "role": roles,
        "voice_example": voice_example
    }

    await redis.set('get_role_voice_example', json.dumps(data), 3600)

    return {"code": "0000", "data": data}


@router.get("/get_voice/{role}/{voice}", tags=["media"])
async def get_voice(role: str, voice: str):
    file_path = os.path.join('./', f'data/voice/model/{role}/参考音频/{voice}')
    file_path = unquote(file_path, encoding='utf-8')
    # print(file_path)
    # 检查文件是否存在
    if not os.path.exists(file_path):
        raise ClientVisibleException("文件不存在")

    # 返回文件内容
    return FileResponse(file_path)


@router.get("/user_model", tags=["media"])
async def get_user_model(
        user: User = Depends(get_request_user)
):
    url = f"{AI_SERVER}/gpt_sovits/user_models"  #
    params = {"user": user.username}

    logger.info(f"user:{user.username}")

    # return user.username

    async with httpx.AsyncClient(timeout=300) as client:
        try:
            response = await client.get(url, params=params)
            response.raise_for_status()
            result = response.json()
            if result.get("code") == 200:
                models = result.get("models", [])
                return {"code": "0000", "data": models}
            else:
                raise ClientVisibleException("获取用户自定义模型失败")
        except httpx.HTTPError as error:
            logger.error(f'An error occurred: {error}')
            raise ClientVisibleException("获取用户自定义模型失败") from error
