/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { GeneratedRoute } from '@elegant-router/types';

export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      i18nKey: 'route.403',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      i18nKey: 'route.404',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      i18nKey: 'route.500',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'about',
    path: '/about',
    component: 'layout.base$view.about',
    meta: {
      title: 'about',
      i18nKey: 'route.about',
      icon: 'fluent:book-information-24-regular',
      order: 10,
      hideInMenu: true
    }
  },
  {
    name: 'aichat',
    path: '/aichat',
    component: 'layout.base',
    meta: {
      title: 'aichat',
      i18nKey: 'route.aichat'
    },
    children: [
      {
        name: 'aichat_assistant',
        path: '/aichat/assistant',
        component: 'view.aichat_assistant',
        meta: {
          title: 'aichat_assistant',
          i18nKey: 'route.aichat_assistant'
        }
      }
    ]
  },
  {
    name: 'asset-manage',
    path: '/asset-manage',
    component: 'layout.base',
    meta: {
      title: 'asset-manage',
      i18nKey: 'route.asset-manage'
    },
    children: [
      {
        name: 'asset-manage_asset-info',
        path: '/asset-manage/asset-info',
        component: 'view.asset-manage_asset-info',
        meta: {
          title: 'asset-manage_asset-info',
          i18nKey: 'route.asset-manage_asset-info'
        }
      },
      {
        name: 'asset-manage_asset-report',
        path: '/asset-manage/asset-report',
        component: 'view.asset-manage_asset-report',
        meta: {
          title: 'asset-manage_asset-report',
          i18nKey: 'route.asset-manage_asset-report'
        }
      },
      {
        name: 'asset-manage_user-assets',
        path: '/asset-manage/user-assets',
        component: 'view.asset-manage_user-assets',
        meta: {
          title: 'asset-manage_user-assets',
          i18nKey: 'route.asset-manage_user-assets'
        }
      },
      {
        name: 'asset-manage_user-report',
        path: '/asset-manage/user-report',
        component: 'view.asset-manage_user-report',
        meta: {
          title: 'asset-manage_user-report',
          i18nKey: 'route.asset-manage_user-report'
        }
      }
    ]
  },
  {
    name: 'audio',
    path: '/audio',
    component: 'layout.base',
    meta: {
      title: 'audio',
      i18nKey: 'route.audio'
    },
    children: [
      {
        name: 'audio_clearvoice',
        path: '/audio/clearvoice',
        component: 'view.audio_clearvoice',
        meta: {
          title: 'audio_clearvoice',
          i18nKey: 'route.audio_clearvoice'
        }
      },
      {
        name: 'audio_cosy',
        path: '/audio/cosy',
        component: 'view.audio_cosy',
        meta: {
          title: 'audio_cosy',
          i18nKey: 'route.audio_cosy'
        }
      },
      {
        name: 'audio_inspiremusic',
        path: '/audio/inspiremusic',
        component: 'view.audio_inspiremusic',
        meta: {
          title: 'audio_inspiremusic',
          i18nKey: 'route.audio_inspiremusic'
        }
      },
      {
        name: 'audio_reduction',
        path: '/audio/reduction',
        component: 'view.audio_reduction',
        meta: {
          title: 'audio_reduction',
          i18nKey: 'route.audio_reduction'
        }
      },
      {
        name: 'audio_separate',
        path: '/audio/separate',
        component: 'view.audio_separate',
        meta: {
          title: 'audio_separate',
          i18nKey: 'route.audio_separate'
        }
      },
      {
        name: 'audio_synthesis',
        path: '/audio/synthesis',
        component: 'view.audio_synthesis',
        meta: {
          title: 'audio_synthesis',
          i18nKey: 'route.audio_synthesis'
        }
      },
      {
        name: 'audio_texttospeech',
        path: '/audio/texttospeech',
        component: 'view.audio_texttospeech',
        meta: {
          title: 'audio_texttospeech',
          i18nKey: 'route.audio_texttospeech'
        }
      },
      {
        name: 'audio_timbres',
        path: '/audio/timbres',
        component: 'view.audio_timbres',
        meta: {
          title: 'audio_timbres',
          i18nKey: 'route.audio_timbres'
        }
      },
      {
        name: 'audio_volcano',
        path: '/audio/volcano',
        component: 'view.audio_volcano',
        meta: {
          title: 'audio_volcano',
          i18nKey: 'route.audio_volcano'
        }
      }
    ]
  },
  {
    name: 'function',
    path: '/function',
    component: 'layout.base',
    meta: {
      title: 'function',
      i18nKey: 'route.function',
      icon: 'icon-park-outline:all-application',
      order: 6,
      hideInMenu: true
    },
    children: [
      {
        name: 'function_hide-child',
        path: '/function/hide-child',
        meta: {
          title: 'function_hide-child',
          i18nKey: 'route.function_hide-child',
          icon: 'material-symbols:filter-list-off',
          order: 2
        },
        redirect: '/function/hide-child/one',
        children: [
          {
            name: 'function_hide-child_one',
            path: '/function/hide-child/one',
            component: 'view.function_hide-child_one',
            meta: {
              title: 'function_hide-child_one',
              i18nKey: 'route.function_hide-child_one',
              icon: 'material-symbols:filter-list-off',
              hideInMenu: true,
              activeMenu: 'function_hide-child'
            }
          },
          {
            name: 'function_hide-child_three',
            path: '/function/hide-child/three',
            component: 'view.function_hide-child_three',
            meta: {
              title: 'function_hide-child_three',
              i18nKey: 'route.function_hide-child_three',
              hideInMenu: true,
              activeMenu: 'function_hide-child'
            }
          },
          {
            name: 'function_hide-child_two',
            path: '/function/hide-child/two',
            component: 'view.function_hide-child_two',
            meta: {
              title: 'function_hide-child_two',
              i18nKey: 'route.function_hide-child_two',
              hideInMenu: true,
              activeMenu: 'function_hide-child'
            }
          }
        ]
      },
      {
        name: 'function_multi-tab',
        path: '/function/multi-tab',
        component: 'view.function_multi-tab',
        meta: {
          title: 'function_multi-tab',
          i18nKey: 'route.function_multi-tab',
          icon: 'ic:round-tab',
          multiTab: true,
          hideInMenu: true,
          activeMenu: 'function_tab'
        }
      },
      {
        name: 'function_request',
        path: '/function/request',
        component: 'view.function_request',
        meta: {
          title: 'function_request',
          i18nKey: 'route.function_request',
          icon: 'carbon:network-overlay',
          order: 3
        }
      },
      {
        name: 'function_super-page',
        path: '/function/super-page',
        component: 'view.function_super-page',
        meta: {
          title: 'function_super-page',
          i18nKey: 'route.function_super-page',
          icon: 'ic:round-supervisor-account',
          order: 5,
          roles: ['R_SUPER']
        }
      },
      {
        name: 'function_tab',
        path: '/function/tab',
        component: 'view.function_tab',
        meta: {
          title: 'function_tab',
          i18nKey: 'route.function_tab',
          icon: 'ic:round-tab',
          order: 1
        }
      },
      {
        name: 'function_toggle-auth',
        path: '/function/toggle-auth',
        component: 'view.function_toggle-auth',
        meta: {
          title: 'function_toggle-auth',
          i18nKey: 'route.function_toggle-auth',
          icon: 'ic:round-construction',
          order: 4
        }
      }
    ]
  },
  {
    name: 'home',
    path: '/home',
    component: 'layout.base$view.home',
    meta: {
      title: 'home',
      i18nKey: 'route.home',
      icon: 'mdi:monitor-dashboard',
      order: 1,
      hideInMenu: true
    }
  },
  {
    name: 'iframe-page',
    path: '/iframe-page/:url',
    component: 'layout.base$view.iframe-page',
    props: true,
    meta: {
      title: 'iframe-page',
      i18nKey: 'route.iframe-page',
      constant: true,
      hideInMenu: true,
      keepAlive: true
    }
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      title: 'login',
      i18nKey: 'route.login',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'manage',
    path: '/manage',
    component: 'layout.base',
    meta: {
      title: 'manage',
      i18nKey: 'route.manage',
      order: 1000,
      icon: 'flowbite:cog-outline'
    },
    children: [
      {
        name: 'manage_creditlog',
        path: '/manage/creditlog',
        component: 'view.manage_creditlog',
        meta: {
          title: 'manage_creditlog',
          i18nKey: 'route.manage_creditlog'
        }
      },
      {
        name: 'manage_menu',
        path: '/manage/menu',
        component: 'view.manage_menu',
        meta: {
          hideInMenu: false,
          title: 'manage_menu',
          i18nKey: 'route.manage_menu',
          icon: 'tabler:list-tree'
        }
      },
      {
        name: 'manage_models',
        path: '/manage/models',
        component: 'view.manage_models',
        meta: {
          title: 'manage_models',
          i18nKey: 'route.manage_models'
        }
      },
      {
        name: 'manage_role',
        path: '/manage/role',
        component: 'view.manage_role',
        meta: {
          hideInMenu: false,
          title: 'manage_role',
          i18nKey: 'route.manage_role',
          icon: 'tabler:user-hexagon'
        }
      },
      {
        name: 'manage_settings',
        path: '/manage/settings',
        component: 'view.manage_settings',
        meta: {
          title: 'manage_settings',
          i18nKey: 'route.manage_settings'
        }
      },
      {
        name: 'manage_system-config',
        path: '/manage/system-config',
        component: 'view.manage_system-config',
        meta: {
          title: 'manage_system-config',
          i18nKey: 'route.manage_system-config'
        },
        children: [
          {
            name: 'manage_system-config_modules',
            path: '/manage/system-config/modules',
            meta: {
              title: 'manage_system-config_modules',
              i18nKey: 'route.manage_system-config_modules'
            },
            children: [
              {
                name: 'manage_system-config_modules_ai-models-setting',
                path: '/manage/system-config/modules/ai-models-setting',
                component: 'view.manage_system-config_modules_ai-models-setting',
                meta: {
                  title: 'manage_system-config_modules_ai-models-setting',
                  i18nKey: 'route.manage_system-config_modules_ai-models-setting'
                }
              }
            ]
          }
        ]
      },
      {
        name: 'manage_user',
        path: '/manage/user',
        component: 'view.manage_user',
        meta: {
          title: 'manage_user',
          i18nKey: 'route.manage_user',
          icon: 'grommet-icons:user'
        }
      },
      {
        name: 'manage_user-credit',
        path: '/manage/user-credit',
        component: 'view.manage_user-credit',
        meta: {
          title: 'manage_user-credit',
          i18nKey: 'route.manage_user-credit'
        }
      },
      {
        name: 'manage_user-detail',
        path: '/manage/user-detail/:id',
        component: 'view.manage_user-detail',
        meta: {
          hideInMenu: true,
          title: 'manage_user-detail',
          i18nKey: 'route.manage_user-detail'
        }
      },
      {
        name: 'manage_workteams',
        path: '/manage/workteams',
        component: 'view.manage_workteams',
        meta: {
          title: 'manage_workteams',
          i18nKey: 'route.manage_workteams'
        }
      }
    ]
  },
  {
    name: 'management',
    path: '/management',
    component: 'layout.base',
    meta: {
      title: 'management',
      i18nKey: 'route.management',
      order: 4,
      icon: 'carbon:gui-management'
    },
    children: [
      {
        name: 'management_channels',
        path: '/management/channels',
        component: 'view.management_channels',
        meta: {
          title: 'management_channels',
          i18nKey: 'route.management_channels'
        }
      },
      {
        name: 'management_credit',
        path: '/management/credit',
        component: 'view.management_credit',
        meta: {
          title: 'management_credit',
          i18nKey: 'route.management_credit'
        }
      },
      {
        name: 'management_game',
        path: '/management/game',
        component: 'view.management_game',
        meta: {
          title: 'management_game',
          i18nKey: 'route.management_game',
          icon: 'tabler:device-gamepad-2'
        }
      },
      {
        name: 'management_toolset',
        path: '/management/toolset',
        component: 'view.management_toolset',
        meta: {
          title: 'management_toolset',
          i18nKey: 'route.management_toolset',
          icon: 'carbon:calendar-tools'
        }
      }
    ]
  },
  {
    name: 'monitor',
    path: '/monitor',
    component: 'layout.base',
    meta: {
      title: 'monitor',
      i18nKey: 'route.monitor'
    },
    children: [
      {
        name: 'monitor_process',
        path: '/monitor/process',
        component: 'view.monitor_process',
        meta: {
          title: 'monitor_process',
          i18nKey: 'route.monitor_process'
        }
      }
    ]
  },
  {
    name: 'multi-menu',
    path: '/multi-menu',
    component: 'layout.base',
    meta: {
      title: 'multi-menu',
      i18nKey: 'route.multi-menu',
      order: 8,
      hideInMenu: true
    },
    children: [
      {
        name: 'multi-menu_first',
        path: '/multi-menu/first',
        meta: {
          title: 'multi-menu_first',
          i18nKey: 'route.multi-menu_first',
          order: 1
        },
        children: [
          {
            name: 'multi-menu_first_child',
            path: '/multi-menu/first/child',
            component: 'view.multi-menu_first_child',
            meta: {
              title: 'multi-menu_first_child',
              i18nKey: 'route.multi-menu_first_child'
            }
          }
        ]
      },
      {
        name: 'multi-menu_second',
        path: '/multi-menu/second',
        meta: {
          title: 'multi-menu_second',
          i18nKey: 'route.multi-menu_second',
          order: 2
        },
        children: [
          {
            name: 'multi-menu_second_child',
            path: '/multi-menu/second/child',
            meta: {
              title: 'multi-menu_second_child',
              i18nKey: 'route.multi-menu_second_child'
            },
            children: [
              {
                name: 'multi-menu_second_child_home',
                path: '/multi-menu/second/child/home',
                component: 'view.multi-menu_second_child_home',
                meta: {
                  title: 'multi-menu_second_child_home',
                  i18nKey: 'route.multi-menu_second_child_home'
                }
              }
            ]
          }
        ]
      }
    ]
  },
  {
    name: 'navigation',
    path: '/navigation',
    component: 'layout.base$view.navigation',
    meta: {
      title: 'navigation',
      i18nKey: 'route.navigation',
      icon: 'tabler:home-search'
    }
  },
  {
    name: 'portrait',
    path: '/portrait',
    component: 'layout.base',
    meta: {
      title: 'portrait',
      i18nKey: 'route.portrait'
    },
    children: [
      {
        name: 'portrait_aidraw',
        path: '/portrait/aidraw',
        component: 'view.portrait_aidraw',
        meta: {
          title: 'portrait_aidraw',
          i18nKey: 'route.portrait_aidraw'
        }
      },
      {
        name: 'portrait_cutout',
        path: '/portrait/cutout',
        component: 'view.portrait_cutout',
        meta: {
          title: 'portrait_cutout',
          i18nKey: 'route.portrait_cutout'
        }
      },
      {
        name: 'portrait_enlarge',
        path: '/portrait/enlarge',
        component: 'view.portrait_enlarge',
        meta: {
          title: 'portrait_enlarge',
          i18nKey: 'route.portrait_enlarge'
        }
      },
      {
        name: 'portrait_facecopy',
        path: '/portrait/facecopy',
        component: 'view.portrait_facecopy',
        meta: {
          title: 'portrait_facecopy',
          i18nKey: 'route.portrait_facecopy'
        }
      },
      {
        name: 'portrait_midjourney',
        path: '/portrait/midjourney',
        component: 'view.portrait_midjourney',
        meta: {
          title: 'portrait_midjourney',
          i18nKey: 'route.portrait_midjourney'
        }
      },
      {
        name: 'portrait_mimicbrush',
        path: '/portrait/mimicbrush',
        component: 'view.portrait_mimicbrush',
        meta: {
          title: 'portrait_mimicbrush',
          i18nKey: 'route.portrait_mimicbrush'
        }
      },
      {
        name: 'portrait_mjworks',
        path: '/portrait/mjworks',
        component: 'view.portrait_mjworks',
        meta: {
          title: 'portrait_mjworks',
          i18nKey: 'route.portrait_mjworks',
          icon: 'mdi:creation',
          hideInMenu: true
        }
      },
      {
        name: 'portrait_sdwebui',
        path: '/portrait/sdwebui',
        component: 'view.portrait_sdwebui',
        meta: {
          title: 'portrait_sdwebui',
          i18nKey: 'route.portrait_sdwebui'
        }
      },
      {
        name: 'portrait_subtitle',
        path: '/portrait/subtitle',
        component: 'view.portrait_subtitle',
        meta: {
          title: 'portrait_subtitle',
          i18nKey: 'route.portrait_subtitle'
        }
      }
    ]
  },
  {
    name: 'share-page',
    path: '/share-page',
    component: 'layout.base$view.share-page',
    meta: {
      title: 'share-page',
      i18nKey: 'route.share-page'
    }
  },
  {
    name: 'stablediffusion',
    path: '/stablediffusion',
    component: 'layout.blank$view.stablediffusion',
    meta: {
      title: 'stablediffusion',
      i18nKey: 'route.stablediffusion'
    }
  },
  {
    name: 'statis',
    path: '/statis',
    component: 'layout.base',
    meta: {
      title: 'statis',
      i18nKey: 'route.statis'
    },
    children: [
      {
        name: 'statis_page',
        path: '/statis/page',
        component: 'view.statis_page',
        meta: {
          title: 'statis_page',
          i18nKey: 'route.statis_page'
        }
      },
      {
        name: 'statis_taskcall',
        path: '/statis/taskcall',
        component: 'view.statis_taskcall',
        meta: {
          title: 'statis_taskcall',
          i18nKey: 'route.statis_taskcall'
        }
      }
    ]
  },
  {
    name: 'text',
    path: '/text',
    component: 'layout.base',
    meta: {
      title: 'text',
      i18nKey: 'route.text',
      icon: 'streamline:chat-bubble-square-write',
      order: 2
    },
    children: [
      {
        name: 'text_copywriting',
        path: '/text/copywriting',
        component: 'view.text_copywriting',
        meta: {
          title: 'text_copywriting',
          i18nKey: 'route.text_copywriting',
          icon: 'ph:pencil-line-duotone'
        }
      },
      {
        name: 'text_imgocr',
        path: '/text/imgocr',
        component: 'view.text_imgocr',
        meta: {
          title: 'text_imgocr',
          i18nKey: 'route.text_imgocr'
        }
      },
      {
        name: 'text_translate',
        path: '/text/translate',
        component: 'view.text_translate',
        meta: {
          title: 'text_translate',
          i18nKey: 'route.text_translate',
          icon: 'ic:twotone-g-translate'
        }
      }
    ]
  },
  {
    name: 'user-center',
    path: '/user-center',
    component: 'layout.base$view.user-center',
    meta: {
      title: 'user-center',
      i18nKey: 'route.user-center',
      hideInMenu: true
    }
  },
  {
    name: 'video',
    path: '/video',
    component: 'layout.base',
    meta: {
      title: 'video',
      i18nKey: 'route.video'
    },
    children: [
      {
        name: 'video_framepack',
        path: '/video/framepack',
        component: 'view.video_framepack',
        meta: {
          title: 'video_framepack',
          i18nKey: 'route.video_framepack'
        }
      }
    ]
  },
  {
    name: 'wiki',
    path: '/wiki',
    component: 'layout.base',
    meta: {
      title: 'wiki',
      i18nKey: 'route.wiki'
    },
    children: [
      {
        name: 'wiki_ainews',
        path: '/wiki/ainews',
        component: 'view.wiki_ainews',
        meta: {
          title: 'wiki_ainews',
          i18nKey: 'route.wiki_ainews'
        }
      },
      {
        name: 'wiki_document',
        path: '/wiki/document',
        component: 'view.wiki_document',
        meta: {
          title: 'wiki_document',
          i18nKey: 'route.wiki_document'
        }
      }
    ]
  }
];
