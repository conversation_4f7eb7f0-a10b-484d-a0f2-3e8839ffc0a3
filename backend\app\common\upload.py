import logging
import os
from fastapi import APIRouter, File, UploadFile, BackgroundTasks
from utils.common import convert_to_mp3
from utils.exceptions import ClientVisibleException

from utils.hash.md5 import md5
import hashlib

router = APIRouter()
logger = logging.getLogger(__name__)

UPLOAD_DIR = "upload"
MAX_FILE_SIZE = 5 * 1024 * 1024 * 1024  # 5GB

if not os.path.exists(UPLOAD_DIR):
  os.makedirs(UPLOAD_DIR)


def calculate_sha1(file_content):
  sha1 = hashlib.sha1()
  sha1.update(file_content)
  return sha1.hexdigest()


async def convert(path: str):
  logger.debug('convert to mp3')
  mp3Path = path.split('.')[0] + '.mp3'
  await convert_to_mp3(path, mp3Path)


@router.post("/upload/{upload_type}")
async def upload_file(background_tasks: BackgroundTasks, file: UploadFile = File(...), upload_type: str = ""):
  # Check file size
  file_size = 0
  content = b""
  try:
    while content_chunk := await file.read(1024 * 1024):
      file_size += len(content_chunk)
      content += content_chunk
      if file_size > MAX_FILE_SIZE:
        raise ClientVisibleException("File too large")

    if not content:
      raise ClientVisibleException("Empty file.")

  except Exception as e:
    raise ClientVisibleException(f"Could not read file. Error: {str(e)}")

  upload_dir_with_type = os.path.join(UPLOAD_DIR, upload_type)
  if not os.path.exists(upload_dir_with_type):
    os.makedirs(upload_dir_with_type,exist_ok=True)

  # Calculate SHA1 and determine file name
  sha1 = calculate_sha1(content)
  file_extension = os.path.splitext(file.filename)[1]
  new_file_name = f"{sha1}{file_extension}"

  # 根据upload_type确定文件路径
  if upload_type == "enlarge_images_zip":
    file_path = os.path.join(UPLOAD_DIR, "enlarge", new_file_name)
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
  else:
    file_path = os.path.join(UPLOAD_DIR,upload_type, new_file_name)

  if not os.path.exists(file_path):
    # Save file
    with open(file_path, "wb") as f:
      f.write(content)
      f.close()

  # 仅在upload_type==subtitle的情况下添加转换任务
  if upload_type == "subtitle":
    background_tasks.add_task(convert, file_path)

  return_data = {
    "code": "0000",
    "msg": "上传成功",
    "data": {
      "file_path": file_path
    }
  }
  return return_data


@router.post("/upload", tags=["common"])
async def upload(file: UploadFile = File(...)):
  fn = md5(file.filename)
  save_path = f'./upload/'
  if not os.path.exists(save_path):
    os.mkdir(save_path)

  save_file = os.path.join(save_path, fn)

  f = open(save_file, 'wb')
  data = await file.read()
  f.write(data)
  f.close()

  return {"msg": f'{fn}上传成功', 'length': len(data)}
