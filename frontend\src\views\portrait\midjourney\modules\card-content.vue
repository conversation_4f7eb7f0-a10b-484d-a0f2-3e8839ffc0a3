<script setup lang="ts">
import { computed, h, nextTick, onMounted, onUnmounted, ref, watch } from 'vue';
import { useDialog, useMessage } from 'naive-ui';
import { useRouter } from 'vue-router';
import type { ButtonType } from '@/service/api/midjourney';
import { changeLike, deleteTask } from '@/service/api/midjourney';
import { ParamsFillBack, encodeParams } from '@/utils/paramsFillBack';
import { ShareType, createShare } from '@/service/api/share';
import { useThemeStore } from '@/store/modules/theme';
import Loading from './loading.vue';

// 定义LoadingItem接口，用于管理loading项及其时间戳
interface LoadingItem {
  id: string; // 唯一标识符
  index: number; // 前端显示索引
  actualIndex: number; // 映射后的后端索引
  timestamp: number; // 创建时间戳
  completed: boolean; // 是否已完成
  startTimestamp?: number; // loading的开始时间戳
}

// 导入路由钩子
const router = useRouter();

interface Props {
  date?: string;
  avatarSrc?: string;
  prompt?: string;
  imageSrc?: string | null;
  image_urls?: string[] | null;
  taskId?: number;
  progress?: string | null;
  action?: string;
  promptEn?: string;
  failReason?: string | null;
  imgSeed?: string | null;
  button?: ButtonType[] | null;
  likeCount?: number;
  isLike?: number;
  id?: number;
  description?: string;
  model?: string;
  manufacturer?: string;
  promptImg?: string[];
  promptImgOss?: string[];
  startTime?: string | null;
  status?: string;
  expectedCount?: number; // 期望生成的图片数量
}

const props = withDefaults(defineProps<Props>(), {
  date: '',
  avatarSrc: '',
  prompt: '',
  imageSrc: '',
  image_urls: null,
  taskId: 0,
  progress: null,
  action: 'IMAGINE',
  promptEn: '',
  failReason: '',
  imgSeed: '',
  button: null,
  likeCount: 0,
  isLike: 0,
  id: 0,
  description: '',
  model: '',
  manufacturer: '',
  promptImg: () => [],
  promptImgOss: () => [],
  startTime: null,
  status: '',
  expectedCount: 1 // 默认期望生成1张图片
});

// 定义占位图URL常量
const PLACEHOLDER_IMAGE_URL = 'https://i.postimg.cc/52YJ745k/AI-2048.png';

// #region 状态变量

// 组件是否已销毁
let isDestroyed = false;

// 加载项状态管理
const loadingList = ref<LoadingItem[]>([]);

// 跟踪已知图片数量，用于计算增量
const lastKnownImageCount = ref(0);

// 显示的图片数组 - 改为直接的响应式状态
const displayImages = ref<
  {
    src: string;
    previewSrc: string;
    isPlaceholder: boolean;
    isFailure: boolean;
    isOpenAILoading: boolean;
    isRemoved?: boolean;
    loadingId?: string;
    startTimestamp?: number;
  }[]
>([]);

// 防抖定时器
let updateTimer: ReturnType<typeof setTimeout> | null = null;

// 将 start_time 字符串转换为时间戳
const startTimeTimestamp = computed(() => {
  if (!props.startTime) return undefined;
  const date = new Date(props.startTime.replace(' ', 'T'));
  return date.getTime();
});

const message = useMessage();
const dialog = useDialog();
const themeStore = useThemeStore();

// 失败容器主题背景色
const failurePlaceholderBgColor = computed(() => {
  return themeStore.darkMode ? '#222428' : '#f5f5f5';
});

// 根据manufacturer的值动态设置头像URL
const dynamicAvatarSrc = computed(() => {
  const manufacturer = String(props.manufacturer || '')
    .trim()
    .toUpperCase();

  if (manufacturer === 'OPENAI') {
    return 'https://xxfpfg-gg.gdsre.cn/aiadmin/images/p/202505/b0f59090bdc4356d490d2fb7bca9decd_20250530155226.png';
  } else if (manufacturer === 'MIDJOURNEY') {
    return 'https://xxfpfg-gg.gdsre.cn/aiadmin/images/p/202505/5eebe9666d0e524b66b2ea81524d4441_20250530180815.png';
  } else if (manufacturer === 'FLUX') {
    return 'https://xxfpfg-gg.gdsre.cn/aiadmin/images/p/202506/d61562a8743b5e8206514a833e224210_20250616175025.png';
  }

  return (
    props.avatarSrc ||
    'https://xxfpfg-gg.gdsre.cn/aiadmin/images/p/202505/5eebe9666d0e524b66b2ea81524d4441_20250530180815.png'
  );
});

// 根据manufacturer的值动态设置Bot名称
const dynamicBotName = computed(() => {
  const manufacturer = String(props.manufacturer || '')
    .trim()
    .toUpperCase();

  if (manufacturer === 'OPENAI') {
    return 'OpenAI Bot';
  } else if (manufacturer === 'FLUX') {
    return 'Flux Bot';
  }
  return 'Midjourney Bot';
});

// 将 progress 转换为 number 类型
const progress = computed(() => {
  if (props.progress === null) {
    return 0;
  } else if (props.progress) {
    return Number.parseInt(props.progress.replace('%', ''), 10);
  }
  return 100;
});

const emit = defineEmits<{
  (event: 'action', payload: { action: string; index: number | 'none'; taskId: number }): void;
  (event: 'jobAction', payload: { label: string; customId: string; taskId: number }): void;
  (
    event: 'importParams',
    payload: {
      action: string;
      prompt: string;
      description: string;
      imageUrl: string | null;
      promptImg?: string[];
    }
  ): void;
  (
    event: 'regenerate',
    payload: { taskId: number; prompt: string; promptImg: string[]; regenerateIndex?: number }
  ): void;
  (
    event: 'fluxRegenerate',
    payload: { taskId: number; prompt: string; promptImg: string[]; regenerateIndex?: number; model: string }
  ): void;
  (
    event: 'panAction',
    payload: { btnPrompt: string; imageUrls: string[]; taskId: number; regenerateIndex?: number }
  ): void;
  (
    event: 'fluxPanAction',
    payload: { btnPrompt: string; imageUrls: string[]; taskId: number; regenerateIndex?: number; model?: string }
  ): void;
  (event: 'delete', payload: { taskId: number }): void;
  (event: 'imagineAgain', payload: { prompt: string; promptImg: string[]; taskId: number }): void;
  (event: 'remove-failure', payload: { taskId: number; index: number }): void;
}>();

const handleAction = (action: string, index: number | 'none') => {
  emit('action', { action, index, taskId: props.taskId });
};

const handleJobAction = (label: string, customId: string) => {
  console.log('点击');
  emit('jobAction', { label, customId, taskId: props.taskId });
  console.log('点击后');
};

// #region 辅助函数

// 生成唯一ID
const generateUniqueId = (): string => {
  return `loading-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
};

// 判断是否是OpenAI或Flux制造商
const isOpenAIOrFlux = (manufacturer: string | undefined) => {
  return (
    String(manufacturer || '')
      .trim()
      .toUpperCase() === 'OPENAI' ||
    String(manufacturer || '')
      .trim()
      .toUpperCase() === 'FLUX'
  );
};

// 辅助函数：处理参考图片数据
const processPromptImages = () => {
  let promptImgArray: string[] = [];

  if (props.promptImg && props.promptImg.length > 0) {
    if (typeof props.promptImg === 'string') {
      try {
        const parsed = JSON.parse(props.promptImg);
        promptImgArray = Array.isArray(parsed) ? parsed : [props.promptImg];
      } catch (e) {
        promptImgArray = [props.promptImg as string];
      }
    } else if (Array.isArray(props.promptImg)) {
      promptImgArray = props.promptImg;
    }
  } else if (props.promptImgOss && props.promptImgOss.length > 0) {
    if (typeof props.promptImgOss === 'string') {
      try {
        const parsed = JSON.parse(props.promptImgOss);
        promptImgArray = Array.isArray(parsed) ? parsed : [props.promptImgOss];
      } catch (e) {
        promptImgArray = [props.promptImgOss as string];
      }
    } else if (Array.isArray(props.promptImgOss)) {
      promptImgArray = props.promptImgOss;
    }
  }

  return promptImgArray;
};

// 辅助函数：映射前端索引到后端索引
const mapDisplayIndexToActual = (index: number, images: any[]) => {
  let adjustCount = 0;
  for (let i = 0; i < index; i++) {
    if (i < images.length && (images[i].isPlaceholder || images[i].isFailure)) {
      adjustCount++;
    }
  }
  return index - adjustCount;
};

// 解析图片URL数组的辅助函数
const parseImageUrls = (imageUrls: string | string[] | null): string[] => {
  if (!imageUrls) return [];

  if (Array.isArray(imageUrls)) return imageUrls;

  if (typeof imageUrls === 'string') {
    try {
      const parsed = JSON.parse(imageUrls);
      if (Array.isArray(parsed)) {
        console.log(`成功解析image_urls字符串为数组，包含${parsed.length}个URL`);
        return parsed;
      }
      console.error('image_urls解析结果不是数组:', parsed);
      return [];
    } catch (e) {
      console.error('解析image_urls失败:', e);
      return [];
    }
  }

  return [];
};

// #endregion 辅助函数

// #region 缓存管理

// 从localStorage缓存加载loading状态
const loadLoadingStateFromCache = () => {
  if (!props.taskId || isDestroyed) return;

  try {
    const cacheKey = `loading-state-${props.taskId}`;
    const cachedData = localStorage.getItem(cacheKey);

    if (!cachedData) return;

    const parsedData = JSON.parse(cachedData);
    if (!Array.isArray(parsedData)) return;

    // 清理过期项
    const currentTime = Date.now();
    const fiveMinutesInMs = 5 * 60 * 1000;
    const validItems = parsedData.filter(item => !item.completed || currentTime - item.timestamp < fiveMinutesInMs);

    if (validItems.length > 0) {
      loadingList.value = validItems;
      console.log(`从缓存恢复了${validItems.length}个loading状态`, validItems);
    }
  } catch (error) {
    console.error('从缓存加载loading状态失败', error);
  }
};

// 保存loading状态到localStorage（防抖）
let saveTimer: ReturnType<typeof setTimeout> | null = null;
const saveLoadingStateToCache = () => {
  if (!props.taskId || isDestroyed) return;

  // 清除之前的定时器
  if (saveTimer) {
    clearTimeout(saveTimer);
  }

  // 防抖保存
  saveTimer = setTimeout(() => {
    if (isDestroyed) return;
    try {
      const cacheKey = `loading-state-${props.taskId}`;
      localStorage.setItem(cacheKey, JSON.stringify(loadingList.value));
    } catch (error) {
      console.error('保存loading状态到缓存失败', error);
    }
  }, 500);
};

// 清除缓存
const clearLoadingStateCache = () => {
  if (!props.taskId) return;
  try {
    const cacheKey = `loading-state-${props.taskId}`;
    localStorage.removeItem(cacheKey);
  } catch (error) {
    console.error('清除loading状态缓存失败', error);
  }
};

// #endregion 缓存管理

// #region 核心状态更新逻辑

// 统一的显示图片更新函数
const updateDisplayImages = () => {
  if (isDestroyed) return;

  console.log('开始更新显示图片');

  try {
    // 解析图片URL数组
    const parsedImageUrls = parseImageUrls(props.image_urls);

    let images: typeof displayImages.value = [];

    // 如果任务失败，显示失败状态
    if (props.failReason || props.status === 'FAILURE') {
      console.log('任务失败，显示失败状态');
      images = [
        {
          src: PLACEHOLDER_IMAGE_URL,
          previewSrc: '',
          isPlaceholder: true,
          isFailure: true,
          isOpenAILoading: false,
          isRemoved: false
        }
      ];
    }
    // 如果有图片URL数组，处理多图模式
    else if (parsedImageUrls.length > 0) {
      console.log(`处理多图模式，共${parsedImageUrls.length}张图片`);

      // 先添加活跃的loading项
      const activeLoadings = loadingList.value.filter(item => !item.completed);
      activeLoadings.forEach(loadingItem => {
        images.push({
          src: PLACEHOLDER_IMAGE_URL,
          previewSrc: '',
          isPlaceholder: true,
          isFailure: false,
          isOpenAILoading: true,
          isRemoved: false,
          loadingId: loadingItem.id,
          startTimestamp: loadingItem.startTimestamp
        });
      });

      // 然后添加实际图片
      parsedImageUrls.forEach(url => {
        if (url === PLACEHOLDER_IMAGE_URL) {
          // 跳过占位图，因为已经通过loading项处理
          return;
        }

        let src = url;
        const isFailure = url === '';

        if (isFailure) {
          images.push({
            src: PLACEHOLDER_IMAGE_URL,
            previewSrc: PLACEHOLDER_IMAGE_URL,
            isPlaceholder: true,
            isFailure: true,
            isOpenAILoading: false,
            isRemoved: false
          });
        } else {
          if (url && url.endsWith('png')) {
            src = `${url}?imageView2/2/h/320/w/320`;
          }
          images.push({
            src,
            previewSrc: url,
            isPlaceholder: false,
            isFailure: false,
            isOpenAILoading: false,
            isRemoved: false
          });
        }
      });
    }
    // 处理单图模式
    else if (props.imageSrc) {
      console.log('处理单图模式');

      // 添加活跃的loading项
      const activeLoadings = loadingList.value.filter(item => !item.completed);
      activeLoadings.forEach(loadingItem => {
        images.push({
          src: PLACEHOLDER_IMAGE_URL,
          previewSrc: '',
          isPlaceholder: true,
          isFailure: false,
          isOpenAILoading: true,
          isRemoved: false,
          loadingId: loadingItem.id,
          startTimestamp: loadingItem.startTimestamp
        });
      });

      // 添加主图片
      const isPlaceholder = !props.imageSrc || props.imageSrc === PLACEHOLDER_IMAGE_URL;
      const isFailure = props.imageSrc === '';

      let src = PLACEHOLDER_IMAGE_URL;
      if (!isPlaceholder && !isFailure && props.imageSrc) {
        if (props.imageSrc.endsWith('png')) {
          src = `${props.imageSrc}?imageView2/2/h/320/w/320`;
        } else {
          src = props.imageSrc;
        }
      }

      images.push({
        src,
        previewSrc: props.imageSrc && props.imageSrc !== '' ? props.imageSrc : PLACEHOLDER_IMAGE_URL,
        isPlaceholder: isPlaceholder || isFailure,
        isFailure: isFailure && isOpenAIOrFlux(props.manufacturer),
        isOpenAILoading:
          isPlaceholder &&
          isOpenAIOrFlux(props.manufacturer) &&
          props.progress !== '100%' &&
          !props.failReason &&
          !isFailure,
        isRemoved: false
      });
    }

    // 更新显示图片
    displayImages.value = images;
    console.log(`显示图片已更新，共${images.length}张`);
  } catch (error) {
    console.error('更新显示图片时出错:', error);
  }
};

// 标记loading项为完成
const markLoadingsCompleted = (count: number) => {
  if (isDestroyed) return;

  console.log(`标记${count}个loading项为完成 (期望总数: ${props.expectedCount})`);

  const activeLoadings = loadingList.value.filter(item => !item.completed);
  const toComplete = Math.min(count, activeLoadings.length);

  // 按时间戳排序，优先完成最早的loading
  const sortedLoadings = [...activeLoadings].sort((a, b) => a.timestamp - b.timestamp);

  for (let i = 0; i < toComplete; i++) {
    const loading = loadingList.value.find(item => item.id === sortedLoadings[i].id);
    if (loading) {
      loading.completed = true;
    }
  }

  // 延迟保存缓存
  saveLoadingStateToCache();

  // 更新显示
  updateDisplayImages();
};

// 统一的状态处理函数（防抖）
const processStateChange = () => {
  if (isDestroyed) return;

  // 清除之前的定时器
  if (updateTimer) {
    clearTimeout(updateTimer);
  }

  // 防抖处理
  updateTimer = setTimeout(() => {
    if (isDestroyed) return;

    console.log('处理状态变化', {
      status: props.status,
      progress: props.progress,
      expectedCount: props.expectedCount,
      taskId: props.taskId
    });

    // 处理任务完成的情况
    if (props.status === 'SUCCESS' && props.progress === '100%') {
      const parsedImageUrls = parseImageUrls(props.image_urls);
      const validImageUrls = parsedImageUrls.filter(url => url !== PLACEHOLDER_IMAGE_URL && url !== '');
      const activeLoadings = loadingList.value.filter(item => !item.completed);

      // 计算新增图片数量
      const newImageCount = validImageUrls.length - lastKnownImageCount.value;

      if (newImageCount > 0) {
        console.log(
          `检测到${newImageCount}张新图片，完成对应loading项 (总数: ${validImageUrls.length}/${props.expectedCount})`
        );
        markLoadingsCompleted(newImageCount);
        lastKnownImageCount.value = validImageUrls.length;
      }

      // 检查是否真正完成任务：基于图片数量而非简单状态判断
      if (validImageUrls.length >= (props.expectedCount || 1)) {
        console.log(`任务真正完成：已生成${validImageUrls.length}张图片，达到期望数量${props.expectedCount}`);
        return;
      }

      // 如果还有activeLoadings但没有新图片，说明可能还在处理中
      if (activeLoadings.length > 0 && newImageCount === 0) {
        console.log(
          `等待更多图片生成中... 当前${validImageUrls.length}/${props.expectedCount}，活跃loading: ${activeLoadings.length}`
        );
      }
    }

    // 处理任务失败的情况
    if (props.status === 'FAILURE' || props.failReason) {
      console.log('任务失败，清理loading状态');
      loadingList.value.forEach(item => {
        item.completed = true;
      });
      lastKnownImageCount.value = 0; // 重置已知图片数量
      saveLoadingStateToCache();
    }

    // 更新显示
    updateDisplayImages();
  }, 100);
};

// #endregion 核心状态更新逻辑

// #region 计算属性

// 过滤出 type 为 JOB 的按钮操作
const jobActions = computed(() => {
  return props.button?.find(button => button.type === 'JOB' || button.type === 'Job')?.actions || [];
});

const isShowAction = computed(() => {
  return !(props.action === 'UPSCALE' || props.action === 'DESCRIBE');
});

const formattedPrompt = computed(() => {
  return props.prompt || '';
});

// 判断任务是否成功完成
const isTaskSuccessful = computed(() => {
  return progress.value === 100 && props.status === 'SUCCESS';
});

// #endregion 计算属性

// #region 状态变量

const is_like = ref(props.isLike);
const like_count = ref(props.likeCount);

// #endregion 状态变量

// #region 交互处理函数

// 处理点赞逻辑
const fetchChangeLike = async () => {
  const response = await changeLike(props.id);

  if (response.data) {
    is_like.value = response.data[0].is_like;
    like_count.value = response.data[0].like_count;
    if (is_like.value === 1) {
      message.success('点赞成功');
    } else {
      message.success('取消点赞');
    }
  }
};

const handleLikeChange = () => {
  fetchChangeLike();
};

// "作为参考图"按钮点击
function handleUseAsReference(imageUrl?: string) {
  const url = imageUrl || props.imageSrc;

  if (!url) {
    message.warning('没有可用的图片');
    return;
  }

  emit('importParams', {
    action: 'DESCRIBE',
    prompt: '',
    description: '',
    imageUrl: url
  });

  message.success('已设置为参考图');
}

// 处理"制作视频"按钮点击
function handleMakeVideo(imageUrl?: string) {
  const url = imageUrl || props.imageSrc;

  if (!url) {
    message.warning('没有可用的图片');
    return;
  }

  const params = {
    prompt_img: url
  };

  router.push({
    path: '/video/framepack',
    query: {
      [ParamsFillBack]: encodeParams(params)
    }
  });
}

function handleImportParams(imageUrl?: string) {
  const url = imageUrl || props.imageSrc;
  const promptImgArray = processPromptImages();

  emit('importParams', {
    action: props.action || '',
    prompt: props.prompt || '',
    description: props.description || '',
    imageUrl: url,
    promptImg: promptImgArray
  });
}

// 处理点击失败状态组件
const handleFailureRegenerate = (index: number) => {
  if (isDestroyed) return;

  const actualIndex = mapDisplayIndexToActual(index, displayImages.value);
  console.log(`前端显示索引: ${index}, 映射到后端实际索引: ${actualIndex}`);

  // 创建新的loading项
  const newLoadingId = generateUniqueId();
  const newLoadingItem: LoadingItem = {
    id: newLoadingId,
    index: 0,
    actualIndex,
    timestamp: Date.now(),
    completed: false,
    startTimestamp: Date.now()
  };

  loadingList.value.push(newLoadingItem);
  saveLoadingStateToCache();

  // 更新显示
  updateDisplayImages();

  // 获取数据并发送事件
  const manufacturer = String(props.manufacturer || '')
    .trim()
    .toUpperCase();
  const promptImgArray = processPromptImages();
  const taskId = props.taskId;
  const prompt = props.prompt || '';
  const model = props.model || '';
  const id = props.id;

  // 发送事件
  emit('remove-failure', { taskId, index: actualIndex });

  try {
    if (manufacturer === 'OPENAI') {
      emit('regenerate', { taskId, prompt, promptImg: promptImgArray, regenerateIndex: actualIndex });
    } else if (manufacturer === 'FLUX') {
      emit('fluxRegenerate', { taskId, prompt, promptImg: promptImgArray, regenerateIndex: actualIndex, model });
    } else if (id) {
      emit('imagineAgain', { prompt, promptImg: props.promptImg || [], taskId: id });
    }
  } catch (error) {
    console.error('发送重新生成事件失败:', error);
    // 清理loading项
    const loadingIndex = loadingList.value.findIndex(item => item.id === newLoadingId);
    if (loadingIndex !== -1) {
      loadingList.value.splice(loadingIndex, 1);
    }
    updateDisplayImages();
    message.error('重新生成失败，请稍后重试');
  }
};

// 通用扩图处理函数
const handlePanActionGeneric = (index: number, imageUrl?: string, regenerateIndex?: number, isFlux = false) => {
  const btnPrompt = `btn${index + 1}`;
  const url = imageUrl || props.imageSrc;
  const imageUrls = url ? [url] : [];
  const taskId = props.taskId;
  const model = props.model || '';

  let actualRegenerateIndex = regenerateIndex;
  if (regenerateIndex !== undefined) {
    actualRegenerateIndex = mapDisplayIndexToActual(regenerateIndex, displayImages.value);
    console.log(
      `${isFlux ? 'Flux' : 'OpenAI'}扩图操作 - 前端显示索引: ${regenerateIndex}, 映射到后端实际索引: ${actualRegenerateIndex}`
    );
  }

  try {
    if (isFlux) {
      emit('fluxPanAction', { btnPrompt, imageUrls, taskId, regenerateIndex: actualRegenerateIndex, model });
    } else {
      emit('panAction', { btnPrompt, imageUrls, taskId, regenerateIndex: actualRegenerateIndex });
    }
  } catch (error) {
    console.error(`发送${isFlux ? 'Flux' : 'OpenAI'}扩图操作事件失败:`, error);
    message.error(`${isFlux ? 'Flux' : 'OpenAI'}扩图操作失败，请稍后重试`);
  }
};

const handlePanAction = (index: number, imageUrl?: string, regenerateIndex?: number) => {
  handlePanActionGeneric(index, imageUrl, regenerateIndex, false);
};

const handleFluxPanAction = (index: number, imageUrl?: string, regenerateIndex?: number) => {
  handlePanActionGeneric(index, imageUrl, regenerateIndex, true);
};

// 处理重新生成按钮点击
function handleRegenerate(_imageUrl?: string, regenerateIndex?: number, actualIndex?: number) {
  if (isDestroyed) return;

  let finalIndex = actualIndex;
  let newLoadingId = '';

  const manufacturer = String(props.manufacturer || '')
    .trim()
    .toUpperCase();
  const promptImgArray = processPromptImages();
  const taskId = props.taskId;
  const prompt = props.prompt || '';
  const model = props.model || '';
  const id = props.id;

  // 如果提供了索引，添加loading项
  if (regenerateIndex !== undefined) {
    finalIndex =
      actualIndex !== undefined ? actualIndex : mapDisplayIndexToActual(regenerateIndex, displayImages.value);
    console.log(`重新生成 - 前端显示索引: ${regenerateIndex}, 映射到后端实际索引: ${finalIndex}`);

    // 创建新的loading项
    newLoadingId = generateUniqueId();
    const newLoadingItem: LoadingItem = {
      id: newLoadingId,
      index: 0,
      actualIndex: finalIndex,
      timestamp: Date.now(),
      completed: false,
      startTimestamp: Date.now()
    };

    loadingList.value.push(newLoadingItem);
    console.log(`已添加loading项: ${newLoadingId}`);

    saveLoadingStateToCache();
    updateDisplayImages();
  }

  // 发送事件
  try {
    if (manufacturer === 'OPENAI') {
      emit('regenerate', {
        taskId,
        prompt,
        promptImg: promptImgArray,
        regenerateIndex: finalIndex !== undefined ? finalIndex : regenerateIndex
      });
    } else if (manufacturer === 'FLUX') {
      emit('fluxRegenerate', {
        taskId,
        prompt,
        promptImg: promptImgArray,
        regenerateIndex: finalIndex !== undefined ? finalIndex : regenerateIndex,
        model
      });
    } else if (id) {
      emit('imagineAgain', { prompt, promptImg: props.promptImg || [], taskId: id });
    }

    console.log(`已发送${manufacturer}重新生成事件，任务ID: ${taskId}`);
  } catch (error) {
    console.error('发送重新生成事件失败:', error);
    // 清理loading项
    if (newLoadingId) {
      const loadingIndex = loadingList.value.findIndex(item => item.id === newLoadingId);
      if (loadingIndex !== -1) {
        loadingList.value.splice(loadingIndex, 1);
      }
      updateDisplayImages();
    }
    message.error('重新生成失败，请稍后重试');
  }
}

// 处理分享按钮点击
const sharing = ref(false);
async function handleShare(imageUrl?: string) {
  const url = imageUrl || props.imageSrc;

  if (!url) {
    message.warning('没有可用的图片');
    return;
  }

  sharing.value = true;
  try {
    const params: Record<string, string | number | string[]> = {
      prompt: props.prompt || ''
    };

    if (props.promptImg && props.promptImg.length > 0) {
      let promptImgArray: string[] = [];

      if (typeof props.promptImg === 'string') {
        try {
          const parsed = JSON.parse(props.promptImg);
          promptImgArray = Array.isArray(parsed) ? parsed : [props.promptImg];
        } catch (e) {
          promptImgArray = [props.promptImg as string];
        }
      } else if (Array.isArray(props.promptImg)) {
        promptImgArray = props.promptImg;
      }

      params.prompt_img = promptImgArray;
    }

    const result: Record<string, string | number> = { url };

    console.log('请求分享API，参数:', { share_type: ShareType.IMAGE, params, result });

    try {
      const shareUrl = await createShare({
        share_type: ShareType.IMAGE,
        params,
        result
      });

      console.log('分享API响应结果:', shareUrl);

      if (!shareUrl) {
        throw new Error('获取分享链接失败：返回结果为空');
      }

      const urlObj = new URL(shareUrl);
      const shareKey = urlObj.searchParams.get('key');

      if (!shareKey) {
        throw new Error('获取分享链接失败：无效的分享Key');
      }

      console.log('成功获取分享Key:', shareKey);

      window.open(shareUrl, '_blank');
      message.success('分享链接已创建成功');
    } catch (apiError) {
      console.error('API调用失败:', apiError);

      message.warning('创建分享链接失败，正在尝试备用方案...');

      const encodedParams = encodeURIComponent(
        JSON.stringify({
          prompt: props.prompt || '',
          result: { url }
        })
      );

      const fallbackShareUrl = `${location.origin}/share-page?temp=true&data=${encodedParams}`;
      window.open(fallbackShareUrl, '_blank');
      message.info('已使用临时链接分享，此链接仅在当前会话有效');
    }
  } catch (e) {
    console.error('创建分享失败:', e);
    message.error(e instanceof Error ? e.message : '创建分享失败，请稍后重试');
  } finally {
    sharing.value = false;
  }
}

// 处理删除按钮点击
const deleting = ref(false);

const handleDelete = async () => {
  if (!props.id) {
    message.warning('无效的任务ID');
    return;
  }

  deleting.value = true;
  try {
    const response = await deleteTask(props.id);
    if (response.data) {
      message.success('记录删除成功');
      emit('delete', { taskId: props.id });
    } else {
      message.error('删除记录失败');
    }
  } catch (error) {
    console.error('删除记录失败:', error);
    message.error('删除记录失败');
  } finally {
    deleting.value = false;
  }
};

// 下拉菜单选项
const dropdownOptions = computed(() => [
  {
    label: '删除记录',
    key: 'delete',
    icon: () =>
      h(
        'svg',
        {
          class: 'w-4 h-4',
          viewBox: '0 0 24 24',
          fill: 'currentColor'
        },
        [h('path', { d: 'M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z' })]
      ),
    props: {}
  }
]);

// 处理下拉菜单选择
const handleDropdownSelect = (key: string) => {
  if (key === 'delete') {
    if (isTaskSuccessful.value) {
      dialog.warning({
        title: '确认删除',
        content: '确认删除这个记录吗？删除后无法恢复。',
        positiveText: '确认',
        negativeText: '取消',
        onPositiveClick: () => {
          handleDelete();
        }
      });
    } else {
      handleDelete();
    }
  }
};

// #endregion 交互处理函数

// #region 组件生命周期

// 初始化loading项
const initializeLoadingItems = () => {
  if (isDestroyed || !isOpenAIOrFlux(props.manufacturer)) return;

  if (props.status === 'SUCCESS' && props.progress === '100%') return;
  if (props.status === 'FAILURE' || props.failReason) {
    console.log('任务已失败，不创建loading项');
    return;
  }

  console.log('初始化loading项...', {
    expectedCount: props.expectedCount,
    taskId: props.taskId,
    manufacturer: props.manufacturer
  });

  const parsedImageUrls = parseImageUrls(props.image_urls);
  const activeLoadings = loadingList.value.filter(item => !item.completed);

  let expectedLoadingCount = 0;
  if (parsedImageUrls.length > 0) {
    expectedLoadingCount = parsedImageUrls.filter(url => url === PLACEHOLDER_IMAGE_URL).length;
    console.log(`检测到${expectedLoadingCount}个占位图，准备创建对应数量的loading项`);
  }

  if (activeLoadings.length >= expectedLoadingCount && expectedLoadingCount > 0) {
    console.log(`已经有${activeLoadings.length}个活跃loading项，无需创建更多`);
    return;
  }

  if (
    expectedLoadingCount === 0 &&
    props.status !== 'SUCCESS' &&
    props.progress !== '100%' &&
    props.status !== 'FAILURE'
  ) {
    expectedLoadingCount = 1;
    console.log('没有检测到占位图但任务未完成，创建1个默认loading项');
  }

  const createCount = expectedLoadingCount - activeLoadings.length;
  if (createCount <= 0) return;

  console.log(`创建${createCount}个新loading项`);

  for (let i = 0; i < createCount; i++) {
    const newLoadingItem: LoadingItem = {
      id: generateUniqueId(),
      index: i,
      actualIndex: i,
      timestamp: Date.now(),
      completed: false,
      startTimestamp: Date.now()
    };

    loadingList.value.push(newLoadingItem);
  }

  if (createCount > 0) {
    saveLoadingStateToCache();
  }
};

// 统一的props监听器 - 合并所有相关监听
watch(
  [() => props.image_urls, () => props.status, () => props.progress, () => props.failReason, () => props.imageSrc],
  (newValues, oldValues) => {
    if (isDestroyed) return;

    // 检查是否真的有变化
    const hasChange = newValues.some((newVal, index) => {
      const oldVal = oldValues?.[index];
      if (Array.isArray(newVal) && Array.isArray(oldVal)) {
        return JSON.stringify(newVal) !== JSON.stringify(oldVal);
      }
      return newVal !== oldVal;
    });

    if (!hasChange) return;

    console.log('检测到props变化，触发状态处理', {
      status: newValues[1],
      progress: newValues[2],
      failReason: newValues[3]
    });

    // 初始化loading项（如果需要）
    initializeLoadingItems();

    // 处理状态变化
    processStateChange();
  },
  { deep: true, immediate: true }
);

// 监听loadingList变化，延迟保存缓存
watch(
  loadingList,
  () => {
    if (isDestroyed) return;
    saveLoadingStateToCache();
  },
  { deep: true }
);

onMounted(() => {
  console.log('组件挂载，加载loading状态缓存');
  loadLoadingStateFromCache();

  // 初始化lastKnownImageCount，基于当前已有的有效图片数量
  const parsedImageUrls = parseImageUrls(props.image_urls);
  const validImageUrls = parsedImageUrls.filter(url => url !== PLACEHOLDER_IMAGE_URL && url !== '');
  lastKnownImageCount.value = validImageUrls.length;
  console.log(`初始化已知图片数量: ${lastKnownImageCount.value}`);

  initializeLoadingItems();

  // 初始更新
  nextTick(() => {
    updateDisplayImages();
  });
});

onUnmounted(() => {
  console.log('组件卸载，清理缓存和定时器');
  isDestroyed = true;

  // 清理定时器
  if (updateTimer) {
    clearTimeout(updateTimer);
  }
  if (saveTimer) {
    clearTimeout(saveTimer);
  }

  // 如果所有loading已完成，清除缓存
  if (!loadingList.value.some(item => !item.completed)) {
    clearLoadingStateCache();
  }
});

// #endregion 组件生命周期
</script>

<template>
  <NCard embedded class="relative">
    <NSpace class="userlike absolute right-3 top-4">
      <NDropdown :options="dropdownOptions" trigger="click" @select="handleDropdownSelect">
        <NButton text>
          <template #icon>
            <SvgIcon icon="fontisto:more-v-a" class="text-1.2em" />
          </template>
        </NButton>
      </NDropdown>

      <NBadge :value="like_count" class="mr-1" type="info">
        <NAvatar class="bg-[#6D6D6D24]">
          <div class="thumb-icon">
            <input type="checkbox" :checked="is_like ? true : false" :value="is_like" @change="handleLikeChange" />
            <svg
              t="1736153332516"
              class="icon"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
            >
              <!-- 给 path 加上统一的类名 thumb，方便 CSS 动画控制 -->
              <path
                class="thumb"
                d="M939.358251 423.424662c-23.01825-37.252439-62.924121-60.779272-107.019409-63.209624-2.755764-0.38681-5.510504-0.579191-8.347109-0.579191l-152.202471-0.121773c6.649444-28.975938 10.015098-58.653865 10.015098-88.657202 0-28.223808-3.213181-57.139372-9.556657-85.952604-0.447185-2.043542-1.098008-4.006244-1.932002-5.866614-15.820314-57.302077-67.37755-96.841605-127.282918-96.841605-72.827679 0-132.081201 59.254545-132.081201 132.081201 0 3.334955 0.132006 6.66991 0.406253 10.015098-2.196015 57.211003-32.108279 109.947088-80.269162 141.363611-14.447037 9.42465-18.524912 28.773324 9.099239 43.220361 9.414417 14.437827 28.752858 18.535145 43.220361 9.099239 65.811892-42.925648 106.429984-115.325585 108.656699-193.684234 0.030699-1.332345-0.010233-2.663666-0.14224-3.996011-0.203638-2.012843-0.304945-4.016477-0.304945-6.019087 0-38.381146 31.233352-69.614497 69.614497-69.614497 32.57593 0 60.474326 22.204721 67.824735 53.997821 0.356111 1.534959 0.823761 3.019777 1.402953 4.453429 4.696975 22.814612 7.076162 45.579081 7.076162 67.743894 0 37.485753-6.222725 74.352405-18.494213 109.592001-3.324722 9.546424-1.819438 20.111037 4.02671 28.345582 5.856381 8.245801 15.332197 13.146415 25.448602 13.156648l193.226816 0.101307c1.423419 0.264013 2.857071 0.426719 4.300956 0.477884 24.116257 0.9967 45.935192 13.614066 58.603723 34.090423 7.838525 12.31242 11.438517 26.800389 10.431583 41.939181-0.080841 0.945535-0.121773 1.911536-0.11154 2.877537 0 0.854461 0.040932 1.697665 0.11154 2.53166 0.010233 0.335644-0.030699 0.661056-0.11154 0.976234-0.101307 0.376577-0.193405 0.772596-0.284479 1.159406l-74.972529 330.391802c-0.914836 1.281179-1.738597 2.6432-2.449795 4.046153-5.937223 11.762905-14.660908 21.48329-25.346271 28.172643-10.746762 6.812149-23.059182 10.614755-35.757388 11.06194-0.854461-0.061398-513.766226-0.224104-513.766226-0.224104-0.467651-0.020466-0.935302-0.030699-1.402953-0.030699 0 0-111.01542 0.172939-112.718201 0.457418-1.932002 0-3.446495-1.50426-3.446495-3.415796l0.299829-416.334173c0-1.901303 1.545192-3.446495 3.01466-3.456728l1.245364 0.121773c1.174756 0.132006 2.653433 0.284479 3.52836 0.193405l83.82822-0.222057 0 339.367221c0 17.253966 13.979386 31.233352 31.233352 31.233352s31.233352-13.979386 31.233352-31.233352L281.009092 435.350273c0-1.778506 0-8.631588 0-10.411117 0-17.253966-13.979386-30.928407-31.233352-30.928407-1.50426 0-117.547183 0.304945-119.402437 0.304945-36.34272 0-65.913199 29.566386-65.913199 65.893756l-0.299829 416.334173c0 36.337603 29.571503 65.902966 65.913199 65.902966 2.541893 0 111.406323-0.457418 111.406323-0.457418 0.457418 0.020466 0.925069 0.030699 1.382487 0.030699 0 0 511.458671 0.274246 512.505513 0.274246 25.469068 0 50.296523-7.197936 71.647807-20.73116 19.612687-12.281721 35.777855-29.881564 46.839795-50.967812 3.660366-5.622044 6.435573-11.875468 8.256034-18.615986 0.11154-0.416486 0.213871-0.823761 0.304945-1.240247l74.881454-330.340637c1.596358-6.212492 2.257413-12.586666 2.00261-18.992563C960.892707 473.366098 953.948551 446.331371 939.358251 423.424662z"
              ></path>
              <!-- 圆形散射效果 -->
            </svg>
            <div class="thumb-circle"></div>
          </div>
        </NAvatar>
      </NBadge>

      <!--
      <NTooltip trigger="hover">
        <template #trigger>
          <NButton strong secondary @click="handleImportParams">
            <template #icon>
              <SvgIcon icon="mdi:logout-variant" />
            </template>
          </NButton>
        </template>
        导入生图参数
      </NTooltip>
      -->
    </NSpace>

    <NSpace :wrap="false">
      <NAvatar round :size="48" :src="dynamicAvatarSrc" class="mt-2">
        <!-- <SvgIcon icon="bx:bot" class="text-4xl" /> -->
      </NAvatar>
      <div>
        <NSpace vertical>
          <div>
            <NText class="mr-2">{{ dynamicBotName }}</NText>
            <NTag type="info">{{ date }}</NTag>
          </div>
          <NTag v-if="imgSeed" round size="small" type="success">Seed: {{ imgSeed }}</NTag>
          <NTooltip trigger="hover" class="max-w-220">
            <template #trigger>
              <!-- 使用 CSS white-space 处理换行 -->
              <NText class="whitespace-pre-wrap">{{ formattedPrompt }}</NText>
            </template>
            {{ promptEn }}
          </NTooltip>
          <div class="w-81">
            <NProgress
              v-if="
                progress !== 100 &&
                String(props.manufacturer || '')
                  .trim()
                  .toUpperCase() !== 'OPENAI' &&
                String(props.manufacturer || '')
                  .trim()
                  .toUpperCase() !== 'FLUX'
              "
              type="line"
              :percentage="progress"
              indicator-placement="inside"
              processing
            />
          </div>
          <NAlert v-if="failReason" type="error">{{ failReason }}</NAlert>

          <!-- 多图片布局 -->
          <NGrid :cols="2" :x-gap="10" :y-gap="5" class="max-w-[600px] w-full">
            <NGi v-for="(image, index) in displayImages" :key="index">
              <div
                class="image-container relative h-full w-full"
                :class="{ 'placeholder-container': image.isPlaceholder }"
              >
                <!-- 为OPENAI模型的占位图使用Loading组件 -->
                <Loading v-if="image.isOpenAILoading" :start-time="image.startTimestamp || startTimeTimestamp" />

                <!-- 失败状态占位容器 -->
                <template v-else-if="image.isFailure">
                  <NCard
                    class="failure-placeholder h-full flex flex-col items-center justify-center"
                    :style="{ backgroundColor: failurePlaceholderBgColor }"
                  >
                    <div class="flex flex-col items-center gap-2">
                      <SvgIcon icon="mdi:image-remove-outline" class="text-5xl" />
                      <NText class="text-lg">图片生成失败</NText>
                      <NButton size="small" type="primary" secondary @click="handleFailureRegenerate(index)">
                        <template #icon>
                          <SvgIcon icon="mingcute:refresh-2-fill" />
                        </template>
                        点击再次生成
                      </NButton>
                    </div>
                  </NCard>
                </template>

                <!-- 正常图片 -->
                <NImage
                  v-else
                  :src="image.src"
                  :preview-src="image.previewSrc"
                  class="h-full w-full"
                  object-fit="contain"
                />

                <!-- 图片内部按钮层 - 只在进度100%时显示，且不是失败状态 -->
                <div v-if="progress === 100 && !image.isFailure" class="image-buttons-overlay">
                  <!-- 上部通用功能按钮 -->
                  <div class="overlay-top-buttons">
                    <template
                      v-if="
                        String(props.manufacturer).trim().toUpperCase() === 'OPENAI' ||
                        String(props.manufacturer).trim().toUpperCase() === 'FLUX' ||
                        props.action === 'UPSCALE'
                      "
                    >
                      <div class="btn-backend">
                        <NButtonGroup size="small">
                          <NTooltip trigger="hover">
                            <template #trigger>
                              <NButton
                                size="small"
                                type="primary"
                                strong
                                secondary
                                :loading="sharing"
                                @click="handleShare(image.previewSrc)"
                              >
                                <template #icon>
                                  <SvgIcon icon="ic:outline-share" />
                                </template>
                              </NButton>
                            </template>
                            分享
                          </NTooltip>

                          <NTooltip trigger="hover">
                            <template #trigger>
                              <NButton
                                size="small"
                                type="primary"
                                strong
                                secondary
                                @click="handleUseAsReference(image.previewSrc)"
                              >
                                <template #icon>
                                  <SvgIcon icon="mynaui:edit-one-solid" />
                                </template>
                              </NButton>
                            </template>
                            作为参考图
                          </NTooltip>

                          <NTooltip v-if="props.action !== 'PAN'" trigger="hover">
                            <template #trigger>
                              <NButton
                                size="small"
                                type="primary"
                                strong
                                secondary
                                @click="handleImportParams(image.previewSrc)"
                              >
                                <template #icon>
                                  <SvgIcon icon="solar:star-ring-bold" />
                                </template>
                              </NButton>
                            </template>
                            制作同款
                          </NTooltip>

                          <NTooltip trigger="hover">
                            <template #trigger>
                              <NButton
                                size="small"
                                type="primary"
                                strong
                                secondary
                                @click="handleMakeVideo(image.previewSrc)"
                              >
                                <template #icon>
                                  <SvgIcon icon="solar:star-fall-broken" />
                                </template>
                              </NButton>
                            </template>
                            制作视频
                          </NTooltip>

                          <NTooltip
                            v-if="
                              String(props.manufacturer).trim().toUpperCase() === 'OPENAI' ||
                              String(props.manufacturer).trim().toUpperCase() === 'FLUX'
                            "
                            trigger="hover"
                          >
                            <template #trigger>
                              <NButton
                                size="small"
                                type="primary"
                                strong
                                secondary
                                @click="handleRegenerate(image.previewSrc, index)"
                              >
                                <template #icon>
                                  <SvgIcon icon="mingcute:refresh-2-fill" />
                                </template>
                              </NButton>
                            </template>
                            重新生成
                          </NTooltip>
                        </NButtonGroup>
                      </div>
                    </template>
                  </div>

                  <!-- 下部扩图按钮 -->
                  <div class="overlay-bottom-buttons">
                    <!-- OPENAI或UPSCALE模式的扩图按钮 -->
                    <template
                      v-if="
                        props.action === 'UPSCALE' ||
                        String(props.manufacturer).trim().toUpperCase() === 'OPENAI' ||
                        String(props.manufacturer).trim().toUpperCase() === 'FLUX'
                      "
                    >
                      <NSpace :size="8" align="center">
                        <div class="btn-backend">
                          <NButtonGroup size="small">
                            <NTooltip
                              v-for="(direction, idx) in ['向左扩图', '向右扩图', '向上扩图', '向下扩图']"
                              :key="direction"
                              trigger="hover"
                            >
                              <template #trigger>
                                <NButton
                                  size="small"
                                  type="info"
                                  strong
                                  secondary
                                  class="expand-btn"
                                  @click="
                                    String(props.manufacturer || '')
                                      .trim()
                                      .toUpperCase() === 'OPENAI'
                                      ? handlePanAction(idx, image.previewSrc, index)
                                      : String(props.manufacturer || '')
                                            .trim()
                                            .toUpperCase() === 'FLUX'
                                        ? handleFluxPanAction(idx, image.previewSrc, index)
                                        : handleJobAction(jobActions[idx + 4]?.label, jobActions[idx + 4]?.customId)
                                  "
                                >
                                  <SvgIcon
                                    :icon="
                                      [
                                        'typcn:arrow-left-outline',
                                        'typcn:arrow-right-outline',
                                        'typcn:arrow-up-outline',
                                        'typcn:arrow-down-outline'
                                      ][idx]
                                    "
                                  />
                                </NButton>
                              </template>
                              {{ jobActions[idx + 4]?.label || direction }}
                            </NTooltip>
                          </NButtonGroup>
                        </div>
                      </NSpace>
                    </template>
                  </div>
                </div>
              </div>
            </NGi>
          </NGrid>

          <!-- MIDJOURNEY 操作按钮 -->
          <div v-if="progress === 100 && status === 'SUCCESS'" class="mt-3">
            <div
              v-if="
                isShowAction &&
                String(props.manufacturer || '')
                  .trim()
                  .toUpperCase() === 'MIDJOURNEY'
              "
              class="flex flex-col gap-4"
            >
              <!-- U系列按钮行 -->
              <div class="flex items-center gap-4">
                <NTooltip v-for="i in 4" :key="`U${i}`" trigger="hover">
                  <template #trigger>
                    <NButton size="medium" type="primary" strong secondary @click="handleAction('UPSCALE', i)">
                      U{{ i }}
                    </NButton>
                  </template>
                  放大第{{ i }}张图片
                </NTooltip>

                <!-- 重新生成按钮 -->
                <div class="flex items-center">
                  <NTooltip trigger="hover">
                    <template #trigger>
                      <NButton
                        size="medium"
                        type="primary"
                        strong
                        secondary
                        @click="handleJobAction(jobActions[4]?.label, jobActions[4]?.customId)"
                      >
                        <SvgIcon icon="mingcute:refresh-2-fill" />
                      </NButton>
                    </template>
                    重新生成
                  </NTooltip>
                </div>
              </div>

              <!-- V系列按钮行 -->
              <div class="flex items-center gap-4.3">
                <NTooltip v-for="i in 4" :key="`V${i}`" trigger="hover">
                  <template #trigger>
                    <NButton size="medium" type="primary" strong secondary @click="handleAction('VARIATION', i)">
                      V{{ i }}
                    </NButton>
                  </template>
                  重新创作第{{ i }}张图片
                </NTooltip>
              </div>
            </div>

            <NGrid
              v-else-if="
                props.action === 'DESCRIBE' &&
                String(props.manufacturer || '')
                  .trim()
                  .toUpperCase() === 'MIDJOURNEY'
              "
              x-gap="22"
              y-gap="5"
              :cols="5"
            >
              <NGi>
                <NTooltip trigger="hover">
                  <template #trigger>
                    <NButton
                      text
                      class="text-5xl"
                      @click="handleJobAction(jobActions[0]?.label, jobActions[0]?.customId)"
                    >
                      <SvgIcon icon="ph:number-one-fill" class="text-blue-400" />
                    </NButton>
                  </template>
                  根据第一个提示词生成图片
                </NTooltip>
              </NGi>

              <NGi>
                <NTooltip trigger="hover">
                  <template #trigger>
                    <NButton
                      text
                      class="text-5xl"
                      @click="handleJobAction(jobActions[1]?.label, jobActions[1]?.customId)"
                    >
                      <SvgIcon icon="ph:number-two-fill" class="text-blue-400" />
                    </NButton>
                  </template>
                  根据第二个提示词生成图片
                </NTooltip>
              </NGi>

              <NGi>
                <NTooltip trigger="hover">
                  <template #trigger>
                    <NButton
                      text
                      class="text-5xl"
                      @click="handleJobAction(jobActions[2]?.label, jobActions[2]?.customId)"
                    >
                      <SvgIcon icon="ph:number-three-fill" class="text-blue-400" />
                    </NButton>
                  </template>
                  根据第三个提示词生成图片
                </NTooltip>
              </NGi>

              <NGi>
                <NTooltip trigger="hover">
                  <template #trigger>
                    <NButton
                      text
                      class="text-5xl"
                      @click="handleJobAction(jobActions[3]?.label, jobActions[3]?.customId)"
                    >
                      <SvgIcon icon="ph:number-four-fill" class="text-blue-400" />
                    </NButton>
                  </template>
                  根据第四个提示词生成图片
                </NTooltip>
              </NGi>
            </NGrid>
          </div>
        </NSpace>
      </div>
    </NSpace>
  </NCard>
</template>

<style scoped>
.thumb-icon {
  width: var(--size);
  position: relative;

  /* 整体缩放 */
  transform: scale(0.7);
  transform-origin: center;
  width: calc(var(--size) * 0.5);

  /* 下面的变量可自行调整颜色 */
  --skin-color: rgb(255, 94, 0); /* 激活时的主色 */
  --gray-color: #999999; /* 未点赞时的灰色 */
  --size: 160px;
  --path-dasharray: 3600;

  input[type='checkbox'] {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 99;
    opacity: 0;
  }
  svg {
    width: 100%;
    position: relative;
    z-index: 9;
    .thumb {
      /* 如果 path 中有 fill，就需要使用 !important 或去掉 path 内置的 fill 来覆盖 */
      fill: var(--gray-color) !important;
      stroke: var(--gray-color) !important;
      stroke-dasharray: var(--path-dasharray);
      stroke-width: 50px;
      stroke-dashoffset: var(--path-dasharray);
      stroke-linecap: round;
    }
  }
  .thumb-circle {
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 1;
    width: 1px;
    height: 1px;
    border-radius: 50%;
    background-color: transparent;
  }
}

/* 点赞后主图标颜色切换的动画 */
@keyframes run {
  0% {
    stroke-dashoffset: var(--path-dasharray);
  }
  80% {
    stroke-dashoffset: 0;
    fill: var(--gray-color);
  }
  100% {
    stroke-dashoffset: 0;
    fill: var(--skin-color);
  }
}

@keyframes touch {
  0%,
  50%,
  100% {
    transform: scale(1);
  }
  25% {
    transform: scale(0.75);
  }
  75% {
    transform: scale(1.25);
  }
}

/* 点击触发动画逻辑 */
input[type='checkbox']:checked + svg {
  animation: touch 0.7s forwards ease-in;
  .thumb {
    stroke: var(--skin-color) !important;
    fill: var(--skin-color) !important;
    animation: run 0.75s 0.1s forwards linear;
  }
}

/* 圆形散射动画 */
@keyframes circle-move {
  0% {
    box-shadow:
      -10px 0 0 3px var(--gray-color),
      0 -10px 0 3px var(--gray-color),
      0 10px 0 3px var(--gray-color),
      10px 0 0 3px var(--gray-color),
      7px 7px 0 3px var(--gray-color),
      -7px 7px 0 3px var(--gray-color),
      7px -7px 0 3px var(--gray-color),
      -7px -7px 0 3px var(--gray-color);
    opacity: 1;
  }
  45% {
    box-shadow:
      -120px 0 0 5px var(--skin-color),
      0 -120px 0 5px var(--skin-color),
      0 120px 0 5px var(--skin-color),
      120px 0 0 5px var(--skin-color),
      90px 90px 0 5px var(--skin-color),
      -90px 90px 0 5px var(--skin-color),
      90px -90px 0 5px var(--skin-color),
      -90px -90px 0 5px var(--skin-color);
    opacity: 0.8;
  }
  80% {
    box-shadow:
      -120px 0 0 7px var(--skin-color),
      0 -120px 0 7px var(--skin-color),
      0 120px 0 7px var(--skin-color),
      120px 0 0 7px var(--skin-color),
      90px 90px 0 7px var(--skin-color),
      -90px 90px 0 7px var(--skin-color),
      90px -90px 0 7px var(--skin-color),
      -90px -90px 0 7px var(--skin-color);
    opacity: 0.5;
  }
  100% {
    box-shadow:
      -120px 0 0 3px var(--skin-color),
      0 -120px 0 3px var(--skin-color),
      0 120px 0 3px var(--skin-color),
      120px 0 0 3px var(--skin-color),
      90px 90px 0 3px var(--skin-color),
      -90px 90px 0 3px var(--skin-color),
      90px -90px 0 3px var(--skin-color),
      -90px -90px 0 3px var(--skin-color);
    opacity: 0;
  }
}

input[type='checkbox']:checked ~ .thumb-circle {
  animation: circle-move 1s 0.2s linear;
}

.expand-btn :deep(.n-button__content) {
  font-size: 16px !important;
}

:deep(.n-image) img {
  width: 100% !important;
}

/* 图片容器悬停效果 */
.image-container {
  position: relative;
  overflow: hidden;
  z-index: 1;
}

/* 按钮悬浮层 */
.image-buttons-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: transparent; /* 移除背景色 */
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 10;
  pointer-events: none; /* 默认不接收鼠标事件，让鼠标事件穿透到图片 */
}

/* 上部按钮样式 */
.overlay-top-buttons {
  /* padding-top: 10px; */
  width: 100%;
  display: flex;
  justify-content: flex-end;
  pointer-events: auto; /* 按钮区域恢复鼠标事件响应 */
}

/* 下部按钮样式 */
.overlay-bottom-buttons {
  /* padding-bottom: 10px; */
  width: 100%;
  display: flex;
  justify-content: flex-end;
  pointer-events: auto; /* 按钮区域恢复鼠标事件响应 */
}

/* 悬浮效果 */
.image-container:hover .image-buttons-overlay {
  opacity: 1;
}

/* 按钮小尺寸调整 */
.image-buttons-overlay :deep(.n-button) {
  padding: 0 8px;
  min-width: 28px;
  height: 28px;
  pointer-events: auto; /* 确保按钮可以点击 */
}

/* MIDJOURNEY按钮组特殊样式 */
.mj-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.btn-backend {
  background: rgba(0, 0, 0, 0.8);
  padding: 0px !important;
  margin: 0px !important;
  border-radius: 10px;
}

:deep(.loading-container) {
  width: 100% !important;
  height: 100% !important;
}

:deep(.userlike) div {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 失败占位容器样式 */
.failure-placeholder {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

:deep(.failure-placeholder) .n-card__content {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
